@echo off
echo 🚀 Iniciando AnalizadorGraficoApp - RunningPips
echo.

echo 📡 Iniciando Backend...
cd backend
start "Backend OCR" cmd /k "npm start"
cd ..

echo ⏳ Esperando 3 segundos...
timeout /t 3 /nobreak > nul

echo 📱 Iniciando Frontend...
start "Frontend Expo" cmd /k "npx expo start"

echo.
echo ✅ Aplicación iniciada!
echo 📡 Backend: http://localhost:5000
echo 📱 Frontend: Expo Dev Tools
echo.
echo Presiona cualquier tecla para cerrar...
pause > nul
