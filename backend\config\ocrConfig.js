// Configuración avanzada para OCR
export const OCR_CONFIG = {
  // Configuración de Tesseract
  tesseract: {
    lang: 'eng',
    options: {
      tessedit_char_whitelist: '0123456789.,ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      tessedit_pageseg_mode: '6', // Uniform block of text
      preserve_interword_spaces: '1',
      user_defined_dpi: '300',
      tessjs_create_hocr: '1',
      tessjs_create_tsv: '1'
    }
  },

  // Patrones de precios mejorados por tipo de instrumento
  pricePatterns: {
    forex_5_decimals: [
      /\b[01]\.\d{4,5}\b/g,           // 1.12345, 0.98765
      /\b[01]\.\d{4,5}[0-9]\b/g       // Con dígito extra
    ],
    forex_3_decimals: [
      /\b\d{2,3}\.\d{2,3}\b/g,        // 123.456, 89.123
      /\b\d{2,3}\.\d{2,3}[0-9]\b/g    // Con dígito extra
    ],
    metals: [
      /\b[12]\d{3}\.\d{1,2}\b/g,      // 1987.65, 2045.30
      /\b[12]\d{3}\b/g                // 1987, 2045
    ],
    crypto: [
      /\b[1-9]\d{4,5}\.\d{1,2}\b/g,   // 45678.90, 123456.78
      /\b[1-9]\d{4,5}\b/g             // 45678, 123456
    ],
    indices: [
      /\b[1-4]\d{4}\.\d{1,2}\b/g,     // 15678.90, 35432.10
      /\b[1-4]\d{4}\b/g               // 15678, 35432
    ]
  },

  // Configuración de limpieza de texto
  textCleaning: {
    removeChars: /[^\w\s\.,]/g,
    normalizeSpaces: /\s+/g,
    currencySymbols: /[$€£¥₹]/g
  },

  // Configuración de validación de precios
  priceValidation: {
    minPriceLength: 3,
    maxPriceLength: 8,
    allowedDecimals: [0, 1, 2, 3, 4, 5]
  }
};

// Función para obtener configuración específica por par
export const getConfigForPair = (pair) => {
  const configs = {
    'XAUUSD': {
      type: 'metals',
      decimals: 2,
      minPrice: 1500,
      maxPrice: 2500,
      patterns: OCR_CONFIG.pricePatterns.metals
    },
    'GBPJPY': {
      type: 'forex_3_decimals',
      decimals: 3,
      minPrice: 140,
      maxPrice: 200,
      patterns: OCR_CONFIG.pricePatterns.forex_3_decimals
    },
    'EURUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 0.9,
      maxPrice: 1.3,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'USDJPY': {
      type: 'forex_3_decimals',
      decimals: 3,
      minPrice: 100,
      maxPrice: 160,
      patterns: OCR_CONFIG.pricePatterns.forex_3_decimals
    },
    'BTCUSD': {
      type: 'crypto',
      decimals: 2,
      minPrice: 20000,
      maxPrice: 100000,
      patterns: OCR_CONFIG.pricePatterns.crypto
    },
    'ETHUSD': {
      type: 'crypto',
      decimals: 2,
      minPrice: 1000,
      maxPrice: 5000,
      patterns: OCR_CONFIG.pricePatterns.crypto
    },
    'AUDUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 0.6,
      maxPrice: 0.9,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'NZDUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 0.5,
      maxPrice: 0.8,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'USDCAD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 1.2,
      maxPrice: 1.5,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'EURJPY': {
      type: 'forex_3_decimals',
      decimals: 3,
      minPrice: 120,
      maxPrice: 170,
      patterns: OCR_CONFIG.pricePatterns.forex_3_decimals
    },
    'GBPUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 1.1,
      maxPrice: 1.5,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'SPX500': {
      type: 'indices',
      decimals: 2,
      minPrice: 3000,
      maxPrice: 6000,
      patterns: OCR_CONFIG.pricePatterns.indices
    },
    'NAS100': {
      type: 'indices',
      decimals: 2,
      minPrice: 10000,
      maxPrice: 20000,
      patterns: OCR_CONFIG.pricePatterns.indices
    },
    'US30': {
      type: 'indices',
      decimals: 2,
      minPrice: 25000,
      maxPrice: 40000,
      patterns: OCR_CONFIG.pricePatterns.indices
    }
  };

  return configs[pair] || null;
};

// Función para limpiar texto OCR
export const cleanOCRText = (text) => {
  if (!text) return '';
  
  return text
    .replace(OCR_CONFIG.textCleaning.removeChars, ' ')
    .replace(OCR_CONFIG.textCleaning.currencySymbols, '')
    .replace(OCR_CONFIG.textCleaning.normalizeSpaces, ' ')
    .trim()
    .toUpperCase();
};

// Función para extraer precios usando patrones específicos
export const extractPricesWithPatterns = (text, patterns) => {
  const prices = [];
  
  for (const pattern of patterns) {
    const matches = text.match(pattern) || [];
    prices.push(...matches.map(match => parseFloat(match)));
  }
  
  return prices
    .filter(price => !isNaN(price) && price > 0)
    .filter((price, index, arr) => arr.indexOf(price) === index) // Eliminar duplicados
    .sort((a, b) => a - b);
};
