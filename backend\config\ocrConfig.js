// Configuración avanzada para OCR
export const OCR_CONFIG = {
  // Configuración de Tesseract
  tesseract: {
    lang: 'eng',
    options: {
      tessedit_char_whitelist: '0123456789.,ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      tessedit_pageseg_mode: '6', // Uniform block of text
      preserve_interword_spaces: '1',
      user_defined_dpi: '300',
      tessjs_create_hocr: '1',
      tessjs_create_tsv: '1'
    }
  },

  // Patrones de precios más flexibles y realistas
  pricePatterns: {
    forex_5_decimals: [
      /\b[01]\.\d{4,5}\b/g,           // 1.12345, 0.98765
      /\b[01],\d{4,5}\b/g,            // 1,12345 (coma decimal)
      /\b[01]\.\d{3,4}\b/g            // 1.1234 (menos decimales)
    ],
    forex_3_decimals: [
      /\b\d{2,3}\.\d{2,3}\b/g,        // 123.456, 89.123
      /\b\d{2,3},\d{2,3}\b/g,         // 123,456 (coma decimal)
      /\b\d{2,3}\.\d{1,2}\b/g         // 123.45 (menos decimales)
    ],
    metals: [
      /\b[12]\d{3}\.\d{1,3}\b/g,      // 1987.65, 2045.301
      /\b[12]\d{3},\d{1,3}\b/g,       // 1987,65 (coma decimal)
      /\b[12]\d{3}\b/g,               // 1987, 2045 (sin decimales)
      /\b[12],\d{3}\.\d{1,2}\b/g      // 1,987.65 (separador miles)
    ],
    crypto: [
      /\b[1-9]\d{4,5}\.\d{1,2}\b/g,   // 45678.90, 123456.78
      /\b[1-9]\d{4,5},\d{1,2}\b/g,    // 45678,90 (coma decimal)
      /\b[1-9]\d{4,5}\b/g,            // 45678, 123456 (sin decimales)
      /\b[1-9]\d,\d{3}\.\d{1,2}\b/g   // 45,678.90 (separador miles)
    ],
    indices: [
      /\b[1-5]\d{3,4}\.\d{1,2}\b/g,   // 15678.90, 35432.10
      /\b[1-5]\d{3,4},\d{1,2}\b/g,    // 15678,90 (coma decimal)
      /\b[1-5]\d{3,4}\b/g,            // 15678, 35432 (sin decimales)
      /\b[1-5]\d,\d{3}\.\d{1,2}\b/g   // 15,678.90 (separador miles)
    ],
    // Patrones generales para cualquier número que parezca precio
    general: [
      /\b\d{1,6}[.,]\d{1,5}\b/g,      // Cualquier número con decimales
      /\b\d{3,6}\b/g                  // Números de 3-6 dígitos sin decimales
    ]
  },

  // Configuración de limpieza de texto
  textCleaning: {
    removeChars: /[^\w\s\.,]/g,
    normalizeSpaces: /\s+/g,
    currencySymbols: /[$€£¥₹]/g
  },

  // Configuración de validación de precios
  priceValidation: {
    minPriceLength: 3,
    maxPriceLength: 8,
    allowedDecimals: [0, 1, 2, 3, 4, 5]
  }
};

// Función para obtener configuración específica por par
export const getConfigForPair = (pair) => {
  const configs = {
    'XAUUSD': {
      type: 'metals',
      decimals: 2,
      minPrice: 1500,
      maxPrice: 2500,
      patterns: OCR_CONFIG.pricePatterns.metals
    },
    'GBPJPY': {
      type: 'forex_3_decimals',
      decimals: 3,
      minPrice: 140,
      maxPrice: 200,
      patterns: OCR_CONFIG.pricePatterns.forex_3_decimals
    },
    'EURUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 0.9,
      maxPrice: 1.3,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'USDJPY': {
      type: 'forex_3_decimals',
      decimals: 3,
      minPrice: 100,
      maxPrice: 160,
      patterns: OCR_CONFIG.pricePatterns.forex_3_decimals
    },
    'BTCUSD': {
      type: 'crypto',
      decimals: 2,
      minPrice: 20000,
      maxPrice: 100000,
      patterns: OCR_CONFIG.pricePatterns.crypto
    },
    'ETHUSD': {
      type: 'crypto',
      decimals: 2,
      minPrice: 1000,
      maxPrice: 5000,
      patterns: OCR_CONFIG.pricePatterns.crypto
    },
    'AUDUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 0.6,
      maxPrice: 0.9,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'NZDUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 0.5,
      maxPrice: 0.8,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'USDCAD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 1.2,
      maxPrice: 1.5,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'EURJPY': {
      type: 'forex_3_decimals',
      decimals: 3,
      minPrice: 120,
      maxPrice: 170,
      patterns: OCR_CONFIG.pricePatterns.forex_3_decimals
    },
    'GBPUSD': {
      type: 'forex_5_decimals',
      decimals: 5,
      minPrice: 1.1,
      maxPrice: 1.5,
      patterns: OCR_CONFIG.pricePatterns.forex_5_decimals
    },
    'SPX500': {
      type: 'indices',
      decimals: 2,
      minPrice: 3000,
      maxPrice: 6000,
      patterns: OCR_CONFIG.pricePatterns.indices
    },
    'NAS100': {
      type: 'indices',
      decimals: 2,
      minPrice: 10000,
      maxPrice: 20000,
      patterns: OCR_CONFIG.pricePatterns.indices
    },
    'US30': {
      type: 'indices',
      decimals: 2,
      minPrice: 25000,
      maxPrice: 40000,
      patterns: OCR_CONFIG.pricePatterns.indices
    }
  };

  return configs[pair] || null;
};

// Función para limpiar texto OCR mejorada
export const cleanOCRText = (text) => {
  if (!text) return '';

  return text
    // Reemplazar caracteres especiales comunes del OCR
    .replace(/[""'']/g, '"')           // Comillas especiales
    .replace(/[–—]/g, '-')             // Guiones especiales
    .replace(/[…]/g, '...')            // Puntos suspensivos
    .replace(/[ﬁﬂ]/g, 'fi')           // Ligaduras
    .replace(/[°]/g, '0')              // Grado por cero
    .replace(/[Oo]/g, '0')             // O por cero en números
    .replace(/[Il|]/g, '1')            // I, l, | por 1
    .replace(/[S]/g, '5')              // S por 5 en contexto numérico
    .replace(/[Z]/g, '2')              // Z por 2 en contexto numérico
    // Limpiar caracteres no deseados pero mantener números y letras
    .replace(/[^\w\s\.,\-+%]/g, ' ')
    .replace(/\s+/g, ' ')              // Normalizar espacios
    .trim()
    .toUpperCase();
};

// Función para extraer precios usando patrones específicos mejorada
export const extractPricesWithPatterns = (text, patterns) => {
  const prices = [];

  // Primero intentar con patrones específicos
  for (const pattern of patterns) {
    const matches = text.match(pattern) || [];
    for (const match of matches) {
      // Normalizar formato (coma por punto)
      const normalizedMatch = match.replace(/,/g, '.');
      // Remover separadores de miles
      const cleanMatch = normalizedMatch.replace(/(\d),(\d{3})/g, '$1$2');
      const price = parseFloat(cleanMatch);
      if (!isNaN(price) && price > 0) {
        prices.push(price);
      }
    }
  }

  // Si no encontramos suficientes precios, usar patrones generales
  if (prices.length < 2) {
    const generalPatterns = OCR_CONFIG.pricePatterns.general;
    for (const pattern of generalPatterns) {
      const matches = text.match(pattern) || [];
      for (const match of matches) {
        const normalizedMatch = match.replace(/,/g, '.');
        const cleanMatch = normalizedMatch.replace(/(\d),(\d{3})/g, '$1$2');
        const price = parseFloat(cleanMatch);
        if (!isNaN(price) && price > 10) { // Filtro mínimo básico
          prices.push(price);
        }
      }
    }
  }

  return prices
    .filter(price => !isNaN(price) && price > 0)
    .filter((price, index, arr) => arr.indexOf(price) === index) // Eliminar duplicados
    .sort((a, b) => a - b);
};
