import vision from '@google-cloud/vision';
import path from 'path';
import fs from 'fs';
import sharp from 'sharp';

const client = new vision.ImageAnnotatorClient({
  keyFilename: path.join(process.cwd(), 'backend', 'google-key.json'),
});

// Función para mejorar calidad de imagen para OCR
const mejorarImagenParaOCR = async (inputPath) => {
  try {
    const outputPath = inputPath.replace(/\.(jpg|jpeg|png)$/i, '_enhanced.png');

    console.log('🔧 Mejorando calidad de imagen para OCR...');

    await sharp(inputPath)
      .resize(null, 1200, {
        withoutEnlargement: false,
        kernel: sharp.kernel.lanczos3
      })
      .sharpen({ sigma: 1, flat: 1, jagged: 2 })
      .normalize()
      .gamma(1.2)
      .png({ quality: 100, compressionLevel: 0 })
      .toFile(outputPath);

    console.log('✅ Imagen mejorada guardada en:', outputPath);
    return outputPath;
  } catch (error) {
    console.log('⚠️ Error mejorando imagen, usando original:', error.message);
    return inputPath;
  }
};

// Pares de divisas soportados con sus características
const PARES_CONFIG = {
  'XAUUSD': { tipo: 'metal', decimales: 2, pipValue: 0.01, rangoNormal: [1800, 2200] },
  'GBPJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [140, 200] },
  'EURUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.9, 1.3] },
  'USDJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [100, 160] },
  'BTCUSD': { tipo: 'crypto', decimales: 2, pipValue: 1, rangoNormal: [20000, 100000] },
  'ETHUSD': { tipo: 'crypto', decimales: 2, pipValue: 0.01, rangoNormal: [1000, 5000] },
  'AUDUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.6, 0.9] },
  'NZDUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.5, 0.8] },
  'USDCAD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [1.2, 1.5] },
  'EURJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [120, 170] },
  'GBPUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [1.1, 1.5] },
  'SPX500': { tipo: 'indice', decimales: 2, pipValue: 0.1, rangoNormal: [4000, 7000] },
  'NAS100': { tipo: 'indice', decimales: 2, pipValue: 0.1, rangoNormal: [10000, 20000] },
  'US30': { tipo: 'indice', decimales: 2, pipValue: 0.1, rangoNormal: [25000, 40000] }
};

// Función para detectar par de divisas con mayor precisión
const detectarPar = (texto) => {
  if (!texto) return null;

  console.log(`🔍 === INICIANDO DETECCIÓN DE PAR ===`);
  console.log(`🔍 Texto original (primeros 300 chars): ${texto.substring(0, 300)}`);

  // ⚡ PRIORIDAD ABSOLUTA: Detectar todas las variaciones de SPX500 que detecta el OCR
  const variacionesQsexs = [
    // Variaciones originales
    'Qsexs000', 'qsexs000', 'QSEXS000', 'Qsexs', 'qsexs', 'QSEXS',
    'qsexsoou', 'QSEXSOOU', 'Qsexsoou', 'qsexs00u', 'QSEXS00U',
    'sexsoou', 'SEXSOOU', 'Sexsoou', 'sexs00u', 'SEXS00U',
    'SPXBO0US', 'spxbo0us', 'Spxbo0us', 'SPXB00US', 'spxb00us',

    // Nuevas variaciones del log actual
    'SPX500USD', 'spx500usd', 'Spx500usd', 'SPX500U5D', 'spx500u5d',
    'SPXS00U', 'spxs00u', 'Spxs00u', 'QSPXS00U', 'qspxs00u',
    'SPX500', 'spx500', 'Spx500', 'SPX5OO', 'spx5oo',

    // Variaciones específicas de los logs recientes
    'SPKS00USD', 'spks00usd', 'Spks00usd', 'SPKS00U5D', 'spks00u5d',
    'SPXS00USD', 'spxs00usd', 'Spxs00usd', 'SPXS00U5D', 'spxs00u5d',
    'QXWU0', 'qxwu0', 'Qxwu0', 'QXWUO', 'qxwuo', 'Qxwuo',

    // Variaciones con números confundidos por OCR
    'SPX5OO', 'spx5oo', 'Spx5oo', 'SPX50O', 'spx50o', 'Spx50o',
    'SPX5O0', 'spx5o0', 'Spx5o0', 'SPX500', 'spx500', 'Spx500'
  ];
  for (const variacion of variacionesQsexs) {
    console.log(`🔍 ⚡ PRIORIDAD ABSOLUTA: Buscando "${variacion}" en texto original`);
    console.log(`🔍 ⚡ Texto contiene "${variacion}": ${texto.includes(variacion)}`);
    if (texto.includes(variacion)) {
      console.log(`✅ ⚡⚡⚡ SPX500 DETECTADO CON PRIORIDAD ABSOLUTA: ${variacion} ⚡⚡⚡`);
      console.log(`🚀 RETORNANDO SPX500 INMEDIATAMENTE - NO BUSCAR MÁS`);
      return 'SPX500';
    }
  }

  // Limpiar texto después de la búsqueda prioritaria
  const textoLimpio = texto.toUpperCase().replace(/[^A-Z0-9]/g, '');
  console.log(`🔍 Texto limpio para detección: ${textoLimpio.substring(0, 200)}`);
  console.log(`🔍 Texto completo contiene QSEXS000: ${textoLimpio.includes('QSEXS000')}`);
  console.log(`🔍 Texto completo contiene QSEXS: ${textoLimpio.includes('QSEXS')}`);
  console.log(`🔍 Texto completo contiene 5797: ${textoLimpio.includes('5797')}`);
  console.log(`🔍 Texto completo contiene 05797: ${textoLimpio.includes('05797')}`);

  // Buscar también en texto limpio para Qsexs
  for (const variacion of variacionesQsexs) {
    const variacionLimpia = variacion.toUpperCase();
    console.log(`🔍 ⚡ PRIORIDAD LIMPIA: Buscando "${variacionLimpia}" en texto limpio`);
    if (textoLimpio.includes(variacionLimpia)) {
      console.log(`✅ ⚡⚡⚡ SPX500 DETECTADO EN TEXTO LIMPIO: ${variacionLimpia} ⚡⚡⚡`);
      return 'SPX500';
    }
  }

  // ⚡ BÚSQUEDA ESPECÍFICA PARA EURJPY (texto grande en centro)
  const variacionesEURJPY = [
    'EURJPY', 'eurjpy', 'Eurjpy', 'EUR JPY', 'eur jpy', 'Eur Jpy',
    'EURJPY,', 'eurjpy,', 'EURJPY 5', 'eurjpy 5', 'EURJPY,5',
    'Euro/Yen', 'euro/yen', 'Euro Yen', 'euro yen', 'EUROYEN',
    'EUR1PY', 'eur1py', 'Eur1py', 'EURJP Y', 'eurjp y'
  ];

  for (const variacion of variacionesEURJPY) {
    console.log(`🔍 ⚡ EURJPY: Buscando "${variacion}" en texto original`);
    if (texto.includes(variacion)) {
      console.log(`✅ ⚡⚡⚡ EURJPY DETECTADO: ${variacion} ⚡⚡⚡`);
      return 'EURJPY';
    }
  }

  // Mostrar fragmento del texto que contiene números
  const fragmentoConNumeros = textoLimpio.match(/.{0,20}[0-9]{4}.{0,20}/g);
  console.log(`🔍 Fragmentos con números: ${fragmentoConNumeros?.slice(0,3).join(' | ') || 'ninguno'}`);

  // ⚡ ORDEN DE PRIORIDAD CORREGIDO: SPX500 PRIMERO, luego otros
  const paresOrdenados = [
    'SPX500',     // 🥇 MÁXIMA PRIORIDAD - Detectar SPX500 PRIMERO
    'NAS100',     // 🥈 Índices similares
    'US30',       // 🥉 Otros índices
    'XAUUSD',     // 🏅 Metales después de índices
    'GBPJPY', 'EURUSD', 'USDJPY', 'BTCUSD', 'ETHUSD',
    'AUDUSD', 'NZDUSD', 'USDCAD', 'EURJPY', 'GBPUSD'
  ];

  // Buscar coincidencias exactas primero con orden de prioridad
  for (const par of paresOrdenados) {
    if (textoLimpio.includes(par)) {
      console.log(`✅ Par detectado exacto: ${par}`);
      return par;
    }
  }

  // Buscar variaciones comunes de SPX500 (más tolerante a errores OCR)
  const variacionesSPX = [
    'SPX500USD', 'SPX500U', 'SPXS00U', 'SPX5OO', 'SP500', 'SPX500',
    'SPXSOOU', 'SPX50OU', 'SPXS0OU', 'SPX5000', 'SPXSOOO',
    'SEXS000', 'SEXS00', 'SEXSOOU', 'SEXS00U',
    'SPXBO0U', 'SPXB00U', 'SPXBO0US', 'SPXB00US'
  ];
  for (const variacion of variacionesSPX) {
    console.log(`🔍 Buscando variación: ${variacion} en texto`);
    console.log(`🔍 Texto contiene "${variacion}": ${textoLimpio.includes(variacion)}`);
    if (textoLimpio.includes(variacion)) {
      console.log(`✅ SPX500 detectado por variación: ${variacion}`);
      return 'SPX500';
    }
  }

  // Buscar patrones específicos que indican SPX500
  if (textoLimpio.includes('5797') || textoLimpio.includes('5801') || textoLimpio.includes('05797') || textoLimpio.includes('05801')) {
    console.log(`✅ SPX500 detectado por precios característicos`);
    return 'SPX500';
  }

  // (Búsqueda de QSEXS000 removida - ya se hace con prioridad arriba)

  // Buscar "500" cerca de otros indicadores o patrones SPX
  if ((textoLimpio.includes('500') && (textoLimpio.includes('SPX') || textoLimpio.includes('SP'))) ||
      textoLimpio.includes('SPXS') || textoLimpio.includes('SP000') || textoLimpio.includes('SP0')) {
    console.log(`✅ SPX500 detectado por fragmentos`);
    return 'SPX500';
  }

  // Buscar con tolerancia a errores OCR
  const candidatos = textoLimpio.match(/[A-Z0-9]{4,7}/g) || [];
  console.log(`🔍 Candidatos encontrados: ${candidatos.join(', ')}`);

  for (const candidato of candidatos) {
    // Similitud especial para SPX500 con umbral más bajo
    if (candidato.includes('SPX') || candidato.includes('SX') || candidato.includes('QS') || candidato.includes('SEX')) {
      const similitudSPX = calcularSimilitud(candidato, 'SPX500');
      if (similitudSPX >= 0.4) {
        console.log(`✅ SPX500 detectado por similitud especial: ${candidato} (similitud: ${similitudSPX.toFixed(2)})`);
        return 'SPX500';
      }
    }

    for (const par of paresOrdenados) {
      const similitud = calcularSimilitud(candidato, par);
      if (similitud >= 0.7) {
        console.log(`✅ Par detectado por similitud: ${par} (${candidato}, similitud: ${similitud.toFixed(2)})`);
        return par;
      }
    }
  }

  console.log(`❌ No se detectó ningún par válido en: ${textoLimpio.substring(0, 100)}`);
  return null;
};

// Función para detectar temporalidad del gráfico
const detectarTemporalidad = (texto) => {
  if (!texto) return 'M15'; // Default

  console.log(`🔍 Buscando temporalidad en texto...`);

  // Patrones de temporalidad COMPLETOS según tu lista
  const temporalidades = [
    // Minutos (m1,m5,m15,m30,m45)
    { patron: /\bm1\b/gi, valor: 'M1' },
    { patron: /\b1m\b/gi, valor: 'M1' },
    { patron: /\bm5\b/gi, valor: 'M5' },
    { patron: /\b5m\b/gi, valor: 'M5' },
    { patron: /\bm15\b/gi, valor: 'M15' },
    { patron: /\b15m\b/gi, valor: 'M15' },
    { patron: /\bm30\b/gi, valor: 'M30' },
    { patron: /\b30m\b/gi, valor: 'M30' },
    { patron: /\bm45\b/gi, valor: 'M45' },
    { patron: /\b45m\b/gi, valor: 'M45' },

    // Horas (h1,h2,h3,h4)
    { patron: /\bh1\b/gi, valor: 'H1' },
    { patron: /\b1h\b/gi, valor: 'H1' },
    { patron: /\bh2\b/gi, valor: 'H2' },
    { patron: /\b2h\b/gi, valor: 'H2' },
    { patron: /\bh3\b/gi, valor: 'H3' },
    { patron: /\b3h\b/gi, valor: 'H3' },
    { patron: /\bh4\b/gi, valor: 'H4' },
    { patron: /\b4h\b/gi, valor: 'H4' },

    // Períodos largos (diario,semanal,mensual,ano)
    { patron: /\bdiario\b/gi, valor: 'D1' },
    { patron: /\bd1\b/gi, valor: 'D1' },
    { patron: /\b1d\b/gi, valor: 'D1' },
    { patron: /\bsemanal\b/gi, valor: 'W1' },
    { patron: /\bw1\b/gi, valor: 'W1' },
    { patron: /\b1w\b/gi, valor: 'W1' },
    { patron: /\bmensual\b/gi, valor: 'MN1' },
    { patron: /\bmn1\b/gi, valor: 'MN1' },
    { patron: /\b1mn\b/gi, valor: 'MN1' },
    { patron: /\bano\b/gi, valor: 'Y1' },
    { patron: /\baño\b/gi, valor: 'Y1' },
    { patron: /\by1\b/gi, valor: 'Y1' },
    { patron: /\b1y\b/gi, valor: 'Y1' }
  ];

  // Buscar cada temporalidad
  for (const temp of temporalidades) {
    const matches = texto.match(temp.patron);
    if (matches && matches.length > 0) {
      console.log(`⏰ Temporalidad encontrada: ${temp.valor} (matches: ${matches.join(', ')})`);
      return temp.valor;
    }
  }

  // Búsqueda más flexible con patrones específicos del OCR
  const patronesFlexibles = [
    // Patrones con espacios y caracteres especiales
    { patron: /\|\s*5m\s*\|/gi, valor: 'M5' },
    { patron: /\|\s*15m\s*\|/gi, valor: 'M15' },
    { patron: /\|\s*30m\s*\|/gi, valor: 'M30' },
    { patron: /\|\s*1h\s*\|/gi, valor: 'H1' },
    { patron: /\|\s*4h\s*\|/gi, valor: 'H4' },

    // Patrones sin barras verticales
    { patron: /\s5m\s/gi, valor: 'M5' },
    { patron: /\s15m\s/gi, valor: 'M15' },
    { patron: /\s30m\s/gi, valor: 'M30' },
    { patron: /\s1h\s/gi, valor: 'H1' },
    { patron: /\s4h\s/gi, valor: 'H4' },

    // Patrones con @ (como en los logs)
    { patron: /@\s*\|\s*5m/gi, valor: 'M5' },
    { patron: /@\s*\|\s*15m/gi, valor: 'M15' },
    { patron: /@\s*\|\s*30m/gi, valor: 'M30' },
    { patron: /@\s*\|\s*1h/gi, valor: 'H1' },
    { patron: /@\s*\|\s*4h/gi, valor: 'H4' },

    // Patrones específicos de los logs recientes
    { patron: /\(\s*B\s*\|\s*5m/gi, valor: 'M5' },
    { patron: /\(\s*B\s*\|\s*15m/gi, valor: 'M15' },
    { patron: /\(\s*B\s*\|\s*1h/gi, valor: 'H1' },

    // Patrones más simples que vimos en los logs
    { patron: /5m\s*\|/gi, valor: 'M5' },
    { patron: /15m\s*\|/gi, valor: 'M15' },
    { patron: /30m\s*\|/gi, valor: 'M30' },
    { patron: /1h\s*\|/gi, valor: 'H1' },
    { patron: /4h\s*\|/gi, valor: 'H4' },

    // Patrones aún más simples
    { patron: /\b5m\b/gi, valor: 'M5' },
    { patron: /\b15m\b/gi, valor: 'M15' },
    { patron: /\b30m\b/gi, valor: 'M30' },
    { patron: /\b1h\b/gi, valor: 'H1' },
    { patron: /\b4h\b/gi, valor: 'H4' }
  ];

  for (const temp of patronesFlexibles) {
    const matches = texto.match(temp.patron);
    if (matches && matches.length > 0) {
      console.log(`⏰ Temporalidad encontrada por patrón flexible: ${temp.valor} (matches: ${matches.join(', ')})`);
      return temp.valor;
    }
  }

  console.log(`⚠️ No se detectó temporalidad específica, usando M15 por defecto`);
  return 'M15';
};

// Función para calcular similitud entre strings
const calcularSimilitud = (str1, str2) => {
  const len1 = str1.length;
  const len2 = str2.length;
  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));

  for (let i = 0; i <= len1; i++) matrix[0][i] = i;
  for (let j = 0; j <= len2; j++) matrix[j][0] = j;

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }

  const distance = matrix[len2][len1];
  return 1 - distance / Math.max(len1, len2);
};

// Función para extraer precios del texto OCR
const extraerPrecios = (texto, par) => {
  if (!texto || !par) return [];

  const config = PARES_CONFIG[par];
  if (!config) return [];

  const [minRango, maxRango] = config.rangoNormal;

  // Patrones específicos según el tipo de instrumento
  let patrones = [];

  if (config.tipo === 'forex' && config.decimales === 5) {
    patrones = [/\b1\.[0-9]{4,5}\b/g, /\b0\.[0-9]{4,5}\b/g];
  } else if (config.tipo === 'forex' && config.decimales === 3) {
    patrones = [/\b[1-2][0-9]{2}\.[0-9]{2,3}\b/g];
  } else if (config.tipo === 'metal') {
    patrones = [/\b[1-2][0-9]{3}\.[0-9]{1,2}\b/g];
  } else if (config.tipo === 'crypto') {
    patrones = [/\b[1-9][0-9]{3,5}\.[0-9]{1,2}\b/g];
  } else if (config.tipo === 'indice') {
    // Patrones más específicos para índices como SPX500
    if (par === 'SPX500') {
      patrones = [
        /\b[4-6][0-9]{3}\.[0-9]{1,2}\b/g,     // 4000.00 - 6999.99
        /\b[4-6][0-9]{3},[0-9]{1,2}\b/g,      // 4000,00 - 6999,99 (coma decimal)
        /\b[4-6][0-9]{3}\b/g,                 // 4000 - 6999 (sin decimales)
        /[0-9]5\.[0-9]{3}\.[0-9]/g,           // Formato específico: 05.797.6
        /H[0-9]\.[0-9]{3},[0-9]/g,            // Formato específico: H5.801,8
        /L[0-9]\.[0-9]{3},[0-9]/g,            // Formato específico: L5.797,6
        /C[0-9]\.[0-9]{3},[0-9]/g,            // Formato específico: C5.801,8
        /O[0-9]\.[0-9]{3},[0-9]/g,            // Formato específico: O5.797,6
        /[0-9]{1,2}\.[0-9]{3},[0-9]/g,        // Formato general: 5.797,6
        /5\.[0-9]{3},[0-9]/g,                 // Formato específico: 5.797,6
        /5\.[0-9]{3}\.[0-9]/g,                // Formato específico: 5.797.6
        /[0-9]{4}\.[0-9]{1,2}/g               // Cualquier número de 4 dígitos con decimales
      ];
    } else if (par === 'NAS100') {
      patrones = [/\b1[0-9]{4}\.[0-9]{1,2}\b/g, /\b1[0-9]{4}\b/g];
    } else if (par === 'US30') {
      patrones = [/\b[2-4][0-9]{4}\.[0-9]{1,2}\b/g, /\b[2-4][0-9]{4}\b/g];
    } else {
      patrones = [/\b[1-9][0-9]{3,4}\.[0-9]{1,2}\b/g, /\b[1-9][0-9]{4,5}\b/g];
    }
  }

  let precios = [];

  for (const patron of patrones) {
    const matches = texto.match(patron) || [];
    console.log(`🔍 Patrón ${patron}: encontrados ${matches.length} matches: [${matches.join(', ')}]`);

    for (const match of matches) {
      // Limpiar el match de caracteres no numéricos excepto punto y coma
      let matchLimpio = match.replace(/[^0-9.,]/g, '');

      // Casos especiales para formatos como "05.797.6" -> "5797.6"
      if (matchLimpio.match(/^0[0-9]\.[0-9]{3}\.[0-9]$/)) {
        matchLimpio = matchLimpio.substring(1).replace(/\.([0-9]{3})\./, '$1.');
      }

      // Casos como "5.797,6" -> "5797.6" (formato específico de los logs)
      if (matchLimpio.match(/^[0-9]\.[0-9]{3},[0-9]$/)) {
        matchLimpio = matchLimpio.replace(/\.([0-9]{3}),/, '$1.');
        console.log(`🔧 Formato 5.797,6 convertido a: ${matchLimpio}`);
      }

      // Casos como "05.797,6" -> "5797.6"
      if (matchLimpio.match(/^0[0-9]\.[0-9]{3},[0-9]$/)) {
        matchLimpio = matchLimpio.substring(1).replace(/\.([0-9]{3}),/, '$1.');
        console.log(`🔧 Formato 05.797,6 convertido a: ${matchLimpio}`);
      }

      // Casos como "5.797)5" -> "5797.5"
      if (matchLimpio.includes(')')) {
        matchLimpio = matchLimpio.replace(/\)/, '.').replace(/\.([0-9]{3})\./, '$1.');
      }

      const precio = parseFloat(matchLimpio.replace(',', '.'));
      console.log(`💰 Evaluando precio: ${match} -> ${matchLimpio} -> ${precio} (rango: ${minRango}-${maxRango})`);

      // Para SPX500, ser más estricto con el rango
      if (par === 'SPX500') {
        if (!isNaN(precio) && precio >= 5000 && precio <= 6000) {
          precios.push(precio);
          console.log(`✅ Precio SPX500 válido agregado: ${precio}`);
        } else {
          console.log(`❌ Precio SPX500 fuera de rango real (5000-6000): ${precio}`);
        }
      } else {
        if (!isNaN(precio) && precio >= minRango && precio <= maxRango) {
          precios.push(precio);
          console.log(`✅ Precio válido agregado: ${precio}`);
        } else {
          console.log(`❌ Precio fuera de rango o inválido: ${precio}`);
        }
      }
    }
  }

  // BÚSQUEDA ESPECÍFICA para EURJPY - Detectar precios reales (160-165)
  if (par === 'EURJPY') {
    console.log(`🔍 === BÚSQUEDA ESPECÍFICA DE PRECIOS EURJPY ===`);

    // Patrones específicos para EURJPY con formato OHLC
    const patronesEURJPY = [
      /O16[0-9]\.[0-9]{3}/g,    // O161.774
      /H16[0-9]\.[0-9]{3}/g,    // H161.778
      /L16[0-9]\.[0-9]{3}/g,    // L161.630
      /C16[0-9]\.[0-9]{3}/g,    // C161.637
      /16[0-9]\.[0-9]{3}/g,     // 161.774
      /16[0-9],[0-9]{3}/g,      // 161,774
      /16[0-9]\,[0-9]{3}/g      // 161,774
    ];

    for (const patron of patronesEURJPY) {
      const matches = texto.match(patron) || [];
      console.log(`🔍 Patrón EURJPY ${patron}: ${matches.join(', ')}`);

      for (const match of matches) {
        let numLimpio = match.replace(/[^0-9.,]/g, '');

        // Convertir 161,774 -> 161.774
        if (numLimpio.includes(',')) {
          numLimpio = numLimpio.replace(',', '.');
        }

        const precio = parseFloat(numLimpio);
        if (!isNaN(precio) && precio >= 160 && precio <= 165) {
          precios.push(precio);
          console.log(`✅ ⚡ PRECIO REAL EURJPY DETECTADO: ${match} -> ${precio}`);
        }
      }
    }

    if (precios.length > 0) {
      console.log(`✅ Precios EURJPY encontrados: ${precios.join(', ')}`);
      // Eliminar duplicados y ordenar
      precios = [...new Set(precios)].sort((a, b) => a - b);
      return precios;
    }
  }

  // BÚSQUEDA FORZADA para SPX500 - Detectar precios reales del formato OHLC
  if (par === 'SPX500') {
    console.log(`🔍 === BÚSQUEDA FORZADA DE PRECIOS SPX500 ===`);

    // Buscar específicamente los patrones OHLC que sabemos que están ahí
    const patronesOHLC = [
      /O5\.[0-9]{3},[0-9]/g,    // O5.797,6
      /H5\.[0-9]{3},[0-9]/g,    // H5.801,8
      /L5\.[0-9]{3},[0-9]/g,    // L5.797,6
      /C5\.[0-9]{3},[0-9]/g,    // C5.801,8
      /5\.[0-9]{3},[0-9]/g,     // 5.797,6
      /5\.[0-9]{3}\.[0-9]/g,    // 5.797.6

      // Patrones adicionales para números grandes
      /5[0-9]{3}\.[0-9]/g,      // 5797.6
      /5[0-9]{3}/g,             // 5797
      /[5-6],[0-9]{3}/g,        // 5,797
      /[5-6]\.[0-9]{3}/g        // 5.797
    ];

    for (const patron of patronesOHLC) {
      const matches = texto.match(patron) || [];
      console.log(`🔍 Patrón OHLC ${patron}: ${matches.join(', ')}`);

      for (const match of matches) {
        let numLimpio = match.replace(/[^0-9.,]/g, '');

        // Convertir 5.797,6 -> 5797.6
        if (numLimpio.match(/^[0-9]\.[0-9]{3},[0-9]$/)) {
          numLimpio = numLimpio.replace(/\.([0-9]{3}),/, '$1.');
        }

        // Convertir 5.797.6 -> 5797.6
        if (numLimpio.match(/^[0-9]\.[0-9]{3}\.[0-9]$/)) {
          numLimpio = numLimpio.replace(/\.([0-9]{3})\./, '$1.');
        }

        // Convertir 5,797 -> 5797
        if (numLimpio.match(/^[0-9],[0-9]{3}$/)) {
          numLimpio = numLimpio.replace(/,([0-9]{3})/, '$1');
        }

        // Convertir 5.797 -> 5797
        if (numLimpio.match(/^[0-9]\.[0-9]{3}$/)) {
          numLimpio = numLimpio.replace(/\.([0-9]{3})/, '$1');
        }

        const precio = parseFloat(numLimpio);
        if (!isNaN(precio) && precio >= 5000 && precio <= 6000) {
          precios.push(precio);
          console.log(`✅ ⚡ PRECIO REAL SPX500 DETECTADO: ${match} -> ${precio}`);
        }
      }
    }

    // Si aún no encontramos nada, buscar manualmente
    if (precios.length === 0) {
      console.log(`🔍 Búsqueda manual de precios SPX500 en texto...`);
      const numerosSospechosos = texto.match(/[0-9]{1,2}[.,][0-9]{3}[.,][0-9]/g) || [];
      console.log(`🔍 Números sospechosos encontrados: ${numerosSospechosos.join(', ')}`);

      for (const num of numerosSospechosos) {
        let numLimpio = num.replace(/[^0-9.,]/g, '');
        if (numLimpio.match(/^[0-9]\.[0-9]{3}\.[0-9]$/)) {
          numLimpio = numLimpio.replace(/\.([0-9]{3})\./, '$1.');
        }
        const precio = parseFloat(numLimpio.replace(',', '.'));
        if (!isNaN(precio) && precio >= 5000 && precio <= 6000) {
          precios.push(precio);
          console.log(`✅ Precio SPX500 encontrado manualmente: ${precio}`);
        }
      }
    }
  }

  // Eliminar duplicados y ordenar
  precios = [...new Set(precios)].sort((a, b) => a - b);

  console.log(`✅ Precios finales para ${par}: ${precios.join(', ')}`);
  return precios;
};

// Implementación de la estrategia RunningPips con temporalidad
const aplicarEstrategiaRunningPips = (par, precios, temporalidad = 'M15') => {
  const config = PARES_CONFIG[par];
  if (!config || precios.length < 2) {
    return {
      precioEntrada: null,
      sl: null,
      tp1: null,
      tp2: null,
      tp3: null,
      direccion: null,
      justificacion: 'Datos insuficientes para análisis'
    };
  }

  // Ordenar precios para identificar estructura
  const preciosOrdenados = [...precios].sort((a, b) => a - b);
  const precioMinimo = preciosOrdenados[0];
  const precioMaximo = preciosOrdenados[preciosOrdenados.length - 1];
  const precioMedio = preciosOrdenados[Math.floor(preciosOrdenados.length / 2)];

  // Calcular rango de precios
  const rango = precioMaximo - precioMinimo;
  const volatilidad = rango / precioMedio;

  // Determinar dirección basada en estructura de precios
  let direccion = 'BUY'; // Por defecto BUY
  let precioEntrada = precioMedio;
  let justificacion = '';

  // Análisis de estructura institucional
  if (volatilidad > 0.02) { // Alta volatilidad
    // Buscar rompimiento de estructura (BOS)
    const tercioSuperior = precioMinimo + (rango * 0.7);
    const tercioInferior = precioMinimo + (rango * 0.3);

    if (precioMedio > tercioSuperior) {
      direccion = 'BUY';
      precioEntrada = tercioInferior; // Entrada en retroceso
      justificacion = '📈 BOS alcista detectado - Entrada en zona de demanda';
    } else if (precioMedio < tercioInferior) {
      direccion = 'SELL';
      precioEntrada = tercioSuperior; // Entrada en retroceso
      justificacion = '📉 BOS bajista detectado - Entrada en zona de oferta';
    } else {
      direccion = 'BUY';
      precioEntrada = precioMinimo + (rango * 0.2);
      justificacion = '⚖️ Consolidación - Entrada en soporte';
    }
  } else {
    // Baja volatilidad - buscar acumulación
    direccion = 'BUY';
    precioEntrada = precioMinimo + (rango * 0.3);
    justificacion = '🔄 Acumulación institucional - Preparación para impulso';
  }

  // Calcular SL y TPs según la estrategia RunningPips CON TEMPORALIDAD
  console.log(`⏰ Aplicando estrategia para temporalidad: ${temporalidad}`);

  // Obtener multiplicadores según temporalidad
  const multiplicadorTempo = getMultiplicadorTemporalidad(temporalidad);
  console.log(`📊 Multiplicador para ${temporalidad}: SL=${multiplicadorTempo.sl}x, TP=${multiplicadorTempo.tp}x`);

  let sl, tp1, tp2, tp3;

  if (direccion === 'BUY') {
    // SL debajo del mínimo reciente (ajustado por temporalidad)
    const slPips = getPipsForPair(par, 'sl') * multiplicadorTempo.sl;
    sl = precioMinimo - (config.pipValue * slPips);

    // TPs escalonados hacia liquidez superior (ajustado por temporalidad)
    const distanciaBase = (precioMaximo - precioEntrada) / 3;
    const distanciaTP = distanciaBase * multiplicadorTempo.tp;

    tp1 = precioEntrada + distanciaTP;
    tp2 = precioEntrada + (distanciaTP * 1.5);
    tp3 = precioEntrada + (distanciaTP * 2.5);

  } else { // SELL
    // SL encima del máximo reciente (ajustado por temporalidad)
    const slPips = getPipsForPair(par, 'sl') * multiplicadorTempo.sl;
    sl = precioMaximo + (config.pipValue * slPips);

    // TPs escalonados hacia liquidez inferior (ajustado por temporalidad)
    const distanciaBase = (precioEntrada - precioMinimo) / 3;
    const distanciaTP = distanciaBase * multiplicadorTempo.tp;

    tp1 = precioEntrada - distanciaTP;
    tp2 = precioEntrada - (distanciaTP * 1.5);
    tp3 = precioEntrada - (distanciaTP * 2.5);
  }

  // Formatear según decimales del par
  const formatear = (precio) => parseFloat(precio.toFixed(config.decimales));

  return {
    precioEntrada: formatear(precioEntrada),
    sl: formatear(sl),
    tp1: formatear(tp1),
    tp2: formatear(tp2),
    tp3: formatear(tp3),
    direccion,
    justificacion,
    volatilidad: parseFloat((volatilidad * 100).toFixed(2)),
    rango: formatear(rango)
  };
};

// Función para obtener multiplicadores según temporalidad
const getMultiplicadorTemporalidad = (temporalidad) => {
  const multiplicadores = {
    // 🔥 SCALPING (Ultra corto plazo) - SL y TP muy pequeños
    'M1': { sl: 0.2, tp: 0.3 },   // 1 minuto: SL mínimo, TP mínimo
    'M5': { sl: 0.4, tp: 0.6 },   // 5 minutos: SL pequeño, TP pequeño

    // ⚡ SCALPING MEDIO (Corto plazo) - SL y TP pequeños
    'M15': { sl: 0.7, tp: 1.0 },  // 15 minutos: SL medio-pequeño, TP medio
    'M30': { sl: 1.0, tp: 1.3 },  // 30 minutos: SL normal, TP normal
    'M45': { sl: 1.2, tp: 1.5 },  // 45 minutos: SL medio, TP medio

    // 📈 SWING TRADING (Mediano plazo) - SL y TP grandes
    'H1': { sl: 1.5, tp: 2.0 },   // 1 hora: SL grande, TP grande
    'H2': { sl: 2.0, tp: 2.5 },   // 2 horas: SL más grande, TP más grande
    'H3': { sl: 2.3, tp: 2.8 },   // 3 horas: SL muy grande, TP muy grande
    'H4': { sl: 2.5, tp: 3.0 },   // 4 horas: SL enorme, TP enorme

    // 🏛️ POSITION TRADING (Largo plazo) - SL y TP enormes
    'D1': { sl: 4.0, tp: 5.0 },   // Diario: SL gigante, TP gigante
    'W1': { sl: 7.0, tp: 9.0 },   // Semanal: SL masivo, TP masivo
    'MN1': { sl: 12.0, tp: 15.0 }, // Mensual: SL colosal, TP colosal
    'Y1': { sl: 20.0, tp: 25.0 }  // Anual: SL épico, TP épico
  };

  return multiplicadores[temporalidad] || multiplicadores['M15']; // Default M15
};

// Función para obtener pips según el par y tipo de nivel
const getPipsForPair = (par, tipo) => {
  const pipsConfig = {
    'XAUUSD': { sl: 200, extension: 300 },
    'GBPJPY': { sl: 30, extension: 50 },
    'EURUSD': { sl: 20, extension: 30 },
    'USDJPY': { sl: 25, extension: 40 },
    'BTCUSD': { sl: 500, extension: 1000 },
    'ETHUSD': { sl: 50, extension: 100 },
    'AUDUSD': { sl: 20, extension: 30 },
    'NZDUSD': { sl: 20, extension: 30 },
    'USDCAD': { sl: 20, extension: 30 },
    'EURJPY': { sl: 30, extension: 50 },
    'GBPUSD': { sl: 25, extension: 40 },
    'SPX500': { sl: 50, extension: 100 },
    'NAS100': { sl: 100, extension: 200 },
    'US30': { sl: 100, extension: 200 }
  };

  return pipsConfig[par]?.[tipo] || 20;
};

export const analizarConGoogleVision = async (req, res) => {
  try {
    const filePath = req.file.path;

    console.log('🔍 Iniciando análisis con Google Vision OCR...');

    // Mejorar calidad de imagen para OCR
    const imagenMejorada = await mejorarImagenParaOCR(filePath);

    // Usar Google Vision para extraer texto
    const [result] = await client.textDetection(imagenMejorada);
    const detections = result.textAnnotations;
    const texto = detections[0]?.description || '';

    console.log("🧠 OCR por Google Vision:", texto.substring(0, 200) + '...');
    console.log("🔍 Texto completo longitud:", texto.length);

    // Detectar par de divisas
    console.log("🔍 Iniciando detección de par...");
    const parDetectado = detectarPar(texto);
    console.log("🔍 Resultado detección:", parDetectado);

    if (!parDetectado) {
      fs.unlinkSync(filePath);
      return res.status(400).json({
        mensaje: 'No se detectó ningún par de divisas válido en la imagen',
        textoDetectado: texto.substring(0, 100)
      });
    }

    console.log(`✅ Par detectado: ${parDetectado}`);

    // Detectar temporalidad ANTES del análisis
    console.log(`🔍 === DETECTANDO TEMPORALIDAD ===`);
    const temporalidad = detectarTemporalidad(texto);
    console.log(`⏰ Temporalidad detectada: ${temporalidad}`);

    // Limpiar texto para análisis
    const textoLimpio = texto.replace(/[^\d.,\s]/g, ' ').replace(/\s+/g, ' ').trim();
    console.log(`🧹 Texto limpio: ${textoLimpio}`);

    // Extraer precios específicos para el par detectado
    console.log(`🔍 === INICIANDO EXTRACCIÓN DE PRECIOS PARA ${parDetectado} ===`);
    console.log(`🔍 Texto contiene O5.797,6: ${texto.includes('O5.797,6')}`);
    console.log(`🔍 Texto contiene H5.801,8: ${texto.includes('H5.801,8')}`);
    console.log(`🔍 Texto contiene L5.797,6: ${texto.includes('L5.797,6')}`);
    console.log(`🔍 Texto contiene C5.801,8: ${texto.includes('C5.801,8')}`);
    const precios = extraerPrecios(texto, parDetectado);

    if (precios.length === 0) {
      console.log(`❌ No se encontraron precios válidos para ${parDetectado}`);

      fs.unlinkSync(filePath);
      return res.status(400).json({
        mensaje: `Se detectó ${parDetectado} pero no se encontraron precios válidos.`,
        par: parDetectado,
        preciosDetectados: [],
        debug: {
          textoDetectado: texto.substring(0, 200),
          textoLimpio: textoLimpio,
          config: PARES_CONFIG[parDetectado]
        }
      });
    }

    console.log(`✅ Precios extraídos: ${precios.length} precios encontrados`);

    // Si solo tenemos 1 precio, generar algunos sintéticos para el análisis
    if (precios.length === 1) {
      const precio = precios[0];
      const variacion = precio * 0.001; // 0.1% de variación

      precios.push(precio - variacion);
      precios.push(precio + variacion);
      precios.sort((a, b) => a - b);

      console.log(`⚠️ Solo 1 precio encontrado, generando precios sintéticos: ${precios.join(', ')}`);
    }

    // Aplicar estrategia RunningPips REAL con temporalidad
    const analisis = aplicarEstrategiaRunningPips(parDetectado, precios, temporalidad);

    // Limpiar archivos
    fs.unlinkSync(filePath);
    if (imagenMejorada !== filePath && fs.existsSync(imagenMejorada)) {
      fs.unlinkSync(imagenMejorada);
    }

    res.json({
      success: true,
      mensaje: 'Análisis completado con éxito',
      par: parDetectado,
      temporalidad: temporalidad,
      ...analisis,
      preciosDetectados: precios,
      totalPrecios: precios.length,
      debug: {
        textoDetectado: texto.substring(0, 200),
        textoLimpio: textoLimpio,
        config: PARES_CONFIG[parDetectado],
        temporalidadDetectada: temporalidad
      }
    });

  } catch (error) {
    console.error('❌ Error usando Google Vision:', error);

    // Limpiar archivos en caso de error
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);

      // También limpiar imagen mejorada si existe
      const imagenMejorada = req.file.path.replace(/\.(jpg|jpeg|png)$/i, '_enhanced.png');
      if (fs.existsSync(imagenMejorada)) {
        fs.unlinkSync(imagenMejorada);
      }
    }

    res.status(500).json({
      mensaje: 'Error procesando imagen con Google Vision OCR.',
      error: error.message
    });
  }
};
