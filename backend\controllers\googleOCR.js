import vision from '@google-cloud/vision';
import path from 'path';
import fs from 'fs';

const client = new vision.ImageAnnotatorClient({
  keyFilename: path.join(process.cwd(), 'backend', 'google-key.json'),
});

export const analizarConGoogleVision = async (req, res) => {
  try {
    const filePath = req.file.path;

    const [result] = await client.textDetection(filePath);
    const detections = result.textAnnotations;
    const texto = detections[0]?.description || '';

    console.log("🧠 OCR por Google Vision:", texto);

    const precios = texto.match(/\d{3,5}[.,]\d{1,5}/g)?.map(p => parseFloat(p.replace(',', '.')));
    const pares = ['XAUUSD', 'GBPJPY', 'EURUSD', 'USDJPY', 'BTCUSD', 'ETHUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'EURJPY', 'GBPUSD', 'SPX500', 'NAS100', 'US30'];
    const parDetectado = pares.find(p => texto.includes(p)) || 'No detectado';

    if (!precios || precios.length < 3) {
      fs.unlinkSync(filePath);
      return res.status(400).json({ mensaje: 'No se detectaron suficientes precios' });
    }

    precios.sort((a, b) => a - b);
    const precioEntrada = precios[Math.floor(precios.length / 2)];
    const sl = Math.min(...precios);
    const tp1 = precioEntrada + 10;
    const tp2 = precioEntrada + 20;
    const tp3 = precioEntrada + 30;

    fs.unlinkSync(filePath);

    res.json({ par: parDetectado, precioEntrada, sl, tp1, tp2, tp3 });
  } catch (error) {
    console.error('❌ Error usando Google Vision:', error);
    res.status(500).json({ mensaje: 'Error procesando imagen con Google Vision OCR.' });
  }
};
