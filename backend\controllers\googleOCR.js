import vision from '@google-cloud/vision';
import path from 'path';
import fs from 'fs';

const client = new vision.ImageAnnotatorClient({
  keyFilename: path.join(process.cwd(), 'backend', 'google-key.json'),
});

// Pares de divisas soportados con sus características
const PARES_CONFIG = {
  'XAUUSD': { tipo: 'metal', decimales: 2, pipValue: 0.01, rangoNormal: [1800, 2200] },
  'GBPJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [140, 200] },
  'EURUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.9, 1.3] },
  'USDJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [100, 160] },
  'BTCUSD': { tipo: 'crypto', decimales: 2, pipValue: 1, rangoNormal: [20000, 100000] },
  'ETHUSD': { tipo: 'crypto', decimales: 2, pipValue: 0.01, rangoNormal: [1000, 5000] },
  'AUDUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.6, 0.9] },
  'NZDUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.5, 0.8] },
  'USDCAD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [1.2, 1.5] },
  'EURJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [120, 170] },
  'GBPUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [1.1, 1.5] },
  'SPX500': { tipo: 'indice', decimales: 2, pipValue: 0.1, rangoNormal: [4000, 7000] },
  'NAS100': { tipo: 'indice', decimales: 2, pipValue: 0.1, rangoNormal: [10000, 20000] },
  'US30': { tipo: 'indice', decimales: 2, pipValue: 0.1, rangoNormal: [25000, 40000] }
};

// Función para detectar par de divisas con mayor precisión
const detectarPar = (texto) => {
  if (!texto) return null;

  // Limpiar texto primero
  const textoLimpio = texto.toUpperCase().replace(/[^A-Z0-9]/g, '');
  console.log(`🔍 Texto limpio para detección: ${textoLimpio.substring(0, 200)}`);
  console.log(`🔍 Texto completo contiene SPXS00U: ${textoLimpio.includes('SPXS00U')}`);
  console.log(`🔍 Texto completo contiene 5797: ${textoLimpio.includes('5797')}`);
  console.log(`🔍 Texto completo contiene SP000: ${textoLimpio.includes('SP000')}`);
  console.log(`🔍 Texto completo contiene SPXS: ${textoLimpio.includes('SPXS')}`);

  // Orden de prioridad: SPX500 antes que US30 para evitar confusiones
  const paresOrdenados = [
    'SPX500', 'XAUUSD', 'GBPJPY', 'EURUSD', 'USDJPY', 'BTCUSD', 'ETHUSD',
    'AUDUSD', 'NZDUSD', 'USDCAD', 'EURJPY', 'GBPUSD', 'NAS100', 'US30'
  ];

  // Buscar coincidencias exactas primero con orden de prioridad
  for (const par of paresOrdenados) {
    if (textoLimpio.includes(par)) {
      console.log(`✅ Par detectado exacto: ${par}`);
      return par;
    }
  }

  // Buscar variaciones comunes de SPX500 (más tolerante a errores OCR)
  const variacionesSPX = [
    'SPX500USD', 'SPX500U', 'SPXS00U', 'SPX5OO', 'SP500', 'SPX500',
    'SPXSOOU', 'SPX50OU', 'SPXS0OU', 'SPX5000', 'SPXSOOO'
  ];
  for (const variacion of variacionesSPX) {
    if (textoLimpio.includes(variacion)) {
      console.log(`✅ SPX500 detectado por variación: ${variacion}`);
      return 'SPX500';
    }
  }

  // Buscar patrones específicos que indican SPX500
  if (textoLimpio.includes('5797') || textoLimpio.includes('5801') || textoLimpio.includes('05797') || textoLimpio.includes('05801')) {
    console.log(`✅ SPX500 detectado por precios característicos`);
    return 'SPX500';
  }

  // Buscar "500" cerca de otros indicadores o patrones SPX
  if ((textoLimpio.includes('500') && (textoLimpio.includes('SPX') || textoLimpio.includes('SP'))) ||
      textoLimpio.includes('SPXS') || textoLimpio.includes('SP000') || textoLimpio.includes('SP0')) {
    console.log(`✅ SPX500 detectado por fragmentos`);
    return 'SPX500';
  }

  // Buscar con tolerancia a errores OCR
  const candidatos = textoLimpio.match(/[A-Z0-9]{4,7}/g) || [];
  console.log(`🔍 Candidatos encontrados: ${candidatos.join(', ')}`);

  for (const candidato of candidatos) {
    for (const par of paresOrdenados) {
      const similitud = calcularSimilitud(candidato, par);
      if (similitud >= 0.7) {
        console.log(`✅ Par detectado por similitud: ${par} (${candidato}, similitud: ${similitud.toFixed(2)})`);
        return par;
      }
    }
  }

  console.log(`❌ No se detectó ningún par válido en: ${textoLimpio.substring(0, 100)}`);
  return null;
};

// Función para calcular similitud entre strings
const calcularSimilitud = (str1, str2) => {
  const len1 = str1.length;
  const len2 = str2.length;
  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));

  for (let i = 0; i <= len1; i++) matrix[0][i] = i;
  for (let j = 0; j <= len2; j++) matrix[j][0] = j;

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }

  const distance = matrix[len2][len1];
  return 1 - distance / Math.max(len1, len2);
};

// Función para extraer precios del texto OCR
const extraerPrecios = (texto, par) => {
  if (!texto || !par) return [];

  const config = PARES_CONFIG[par];
  if (!config) return [];

  const [minRango, maxRango] = config.rangoNormal;

  // Patrones específicos según el tipo de instrumento
  let patrones = [];

  if (config.tipo === 'forex' && config.decimales === 5) {
    patrones = [/\b1\.[0-9]{4,5}\b/g, /\b0\.[0-9]{4,5}\b/g];
  } else if (config.tipo === 'forex' && config.decimales === 3) {
    patrones = [/\b[1-2][0-9]{2}\.[0-9]{2,3}\b/g];
  } else if (config.tipo === 'metal') {
    patrones = [/\b[1-2][0-9]{3}\.[0-9]{1,2}\b/g];
  } else if (config.tipo === 'crypto') {
    patrones = [/\b[1-9][0-9]{3,5}\.[0-9]{1,2}\b/g];
  } else if (config.tipo === 'indice') {
    // Patrones más específicos para índices como SPX500
    if (par === 'SPX500') {
      patrones = [
        /\b[4-6][0-9]{3}\.[0-9]{1,2}\b/g,     // 4000.00 - 6999.99
        /\b[4-6][0-9]{3},[0-9]{1,2}\b/g,      // 4000,00 - 6999,99 (coma decimal)
        /\b[4-6][0-9]{3}\b/g,                 // 4000 - 6999 (sin decimales)
        /[0-9]5\.[0-9]{3}\.[0-9]/g,           // Formato específico: 05.797.6
        /H[0-9]\.[0-9]{3},[0-9]/g,            // Formato específico: H5.801,8
        /L[0-9]\.[0-9]{3}\)[0-9]/g,           // Formato específico: L5.797)5
        /C[0-9]\.[0-9]{3},[0-9]/g,            // Formato específico: C5.501,8
        /[0-9]{4}\.[0-9]{1,2}/g               // Cualquier número de 4 dígitos con decimales
      ];
    } else if (par === 'NAS100') {
      patrones = [/\b1[0-9]{4}\.[0-9]{1,2}\b/g, /\b1[0-9]{4}\b/g];
    } else if (par === 'US30') {
      patrones = [/\b[2-4][0-9]{4}\.[0-9]{1,2}\b/g, /\b[2-4][0-9]{4}\b/g];
    } else {
      patrones = [/\b[1-9][0-9]{3,4}\.[0-9]{1,2}\b/g, /\b[1-9][0-9]{4,5}\b/g];
    }
  }

  let precios = [];

  for (const patron of patrones) {
    const matches = texto.match(patron) || [];
    console.log(`🔍 Patrón ${patron}: encontrados ${matches.length} matches: [${matches.join(', ')}]`);

    for (const match of matches) {
      // Limpiar el match de caracteres no numéricos excepto punto y coma
      let matchLimpio = match.replace(/[^0-9.,]/g, '');

      // Casos especiales para formatos como "05.797.6" -> "5797.6"
      if (matchLimpio.match(/^0[0-9]\.[0-9]{3}\.[0-9]$/)) {
        matchLimpio = matchLimpio.substring(1).replace(/\.([0-9]{3})\./, '$1.');
      }

      // Casos como "5.797)5" -> "5797.5"
      if (matchLimpio.includes(')')) {
        matchLimpio = matchLimpio.replace(/\)/, '.').replace(/\.([0-9]{3})\./, '$1.');
      }

      const precio = parseFloat(matchLimpio.replace(',', '.'));
      console.log(`💰 Evaluando precio: ${match} -> ${matchLimpio} -> ${precio} (rango: ${minRango}-${maxRango})`);

      if (!isNaN(precio) && precio >= minRango && precio <= maxRango) {
        precios.push(precio);
        console.log(`✅ Precio válido agregado: ${precio}`);
      } else {
        console.log(`❌ Precio fuera de rango o inválido: ${precio}`);
      }
    }
  }

  // Si no encontramos precios con patrones, buscar manualmente para SPX500
  if (precios.length === 0 && par === 'SPX500') {
    console.log(`🔍 Búsqueda manual de precios SPX500 en texto...`);

    // Buscar números que parecen precios de SPX500
    const numerosSospechosos = texto.match(/[0-9]{1,2}[.,][0-9]{3}[.,][0-9]/g) || [];
    console.log(`🔍 Números sospechosos encontrados: ${numerosSospechosos.join(', ')}`);

    for (const num of numerosSospechosos) {
      let numLimpio = num.replace(/[^0-9.,]/g, '');

      // Convertir formatos como "5.797.6" a "5797.6"
      if (numLimpio.match(/^[0-9]\.[0-9]{3}\.[0-9]$/)) {
        numLimpio = numLimpio.replace(/\.([0-9]{3})\./, '$1.');
      }

      const precio = parseFloat(numLimpio.replace(',', '.'));
      if (!isNaN(precio) && precio >= 4000 && precio <= 7000) {
        precios.push(precio);
        console.log(`✅ Precio SPX500 encontrado manualmente: ${precio}`);
      }
    }
  }

  // Eliminar duplicados y ordenar
  precios = [...new Set(precios)].sort((a, b) => a - b);

  console.log(`✅ Precios finales para ${par}: ${precios.join(', ')}`);
  return precios;
};

// Implementación de la estrategia RunningPips
const aplicarEstrategiaRunningPips = (par, precios) => {
  const config = PARES_CONFIG[par];
  if (!config || precios.length < 2) {
    return {
      precioEntrada: null,
      sl: null,
      tp1: null,
      tp2: null,
      tp3: null,
      direccion: null,
      justificacion: 'Datos insuficientes para análisis'
    };
  }

  // Ordenar precios para identificar estructura
  const preciosOrdenados = [...precios].sort((a, b) => a - b);
  const precioMinimo = preciosOrdenados[0];
  const precioMaximo = preciosOrdenados[preciosOrdenados.length - 1];
  const precioMedio = preciosOrdenados[Math.floor(preciosOrdenados.length / 2)];

  // Calcular rango de precios
  const rango = precioMaximo - precioMinimo;
  const volatilidad = rango / precioMedio;

  // Determinar dirección basada en estructura de precios
  let direccion = 'BUY'; // Por defecto BUY
  let precioEntrada = precioMedio;
  let justificacion = '';

  // Análisis de estructura institucional
  if (volatilidad > 0.02) { // Alta volatilidad
    // Buscar rompimiento de estructura (BOS)
    const tercioSuperior = precioMinimo + (rango * 0.7);
    const tercioInferior = precioMinimo + (rango * 0.3);

    if (precioMedio > tercioSuperior) {
      direccion = 'BUY';
      precioEntrada = tercioInferior; // Entrada en retroceso
      justificacion = '📈 BOS alcista detectado - Entrada en zona de demanda';
    } else if (precioMedio < tercioInferior) {
      direccion = 'SELL';
      precioEntrada = tercioSuperior; // Entrada en retroceso
      justificacion = '📉 BOS bajista detectado - Entrada en zona de oferta';
    } else {
      direccion = 'BUY';
      precioEntrada = precioMinimo + (rango * 0.2);
      justificacion = '⚖️ Consolidación - Entrada en soporte';
    }
  } else {
    // Baja volatilidad - buscar acumulación
    direccion = 'BUY';
    precioEntrada = precioMinimo + (rango * 0.3);
    justificacion = '🔄 Acumulación institucional - Preparación para impulso';
  }

  // Calcular SL y TPs según la estrategia RunningPips
  let sl, tp1, tp2, tp3;

  if (direccion === 'BUY') {
    // SL debajo del mínimo reciente
    sl = precioMinimo - (config.pipValue * getPipsForPair(par, 'sl'));

    // TPs escalonados hacia liquidez superior
    const distanciaTP = (precioMaximo - precioEntrada) / 3;
    tp1 = precioEntrada + distanciaTP;
    tp2 = precioEntrada + (distanciaTP * 2);
    tp3 = precioMaximo + (config.pipValue * getPipsForPair(par, 'extension'));

  } else { // SELL
    // SL encima del máximo reciente
    sl = precioMaximo + (config.pipValue * getPipsForPair(par, 'sl'));

    // TPs escalonados hacia liquidez inferior
    const distanciaTP = (precioEntrada - precioMinimo) / 3;
    tp1 = precioEntrada - distanciaTP;
    tp2 = precioEntrada - (distanciaTP * 2);
    tp3 = precioMinimo - (config.pipValue * getPipsForPair(par, 'extension'));
  }

  // Formatear según decimales del par
  const formatear = (precio) => parseFloat(precio.toFixed(config.decimales));

  return {
    precioEntrada: formatear(precioEntrada),
    sl: formatear(sl),
    tp1: formatear(tp1),
    tp2: formatear(tp2),
    tp3: formatear(tp3),
    direccion,
    justificacion,
    volatilidad: parseFloat((volatilidad * 100).toFixed(2)),
    rango: formatear(rango)
  };
};

// Función para obtener pips según el par y tipo de nivel
const getPipsForPair = (par, tipo) => {
  const pipsConfig = {
    'XAUUSD': { sl: 200, extension: 300 },
    'GBPJPY': { sl: 30, extension: 50 },
    'EURUSD': { sl: 20, extension: 30 },
    'USDJPY': { sl: 25, extension: 40 },
    'BTCUSD': { sl: 500, extension: 1000 },
    'ETHUSD': { sl: 50, extension: 100 },
    'AUDUSD': { sl: 20, extension: 30 },
    'NZDUSD': { sl: 20, extension: 30 },
    'USDCAD': { sl: 20, extension: 30 },
    'EURJPY': { sl: 30, extension: 50 },
    'GBPUSD': { sl: 25, extension: 40 },
    'SPX500': { sl: 50, extension: 100 },
    'NAS100': { sl: 100, extension: 200 },
    'US30': { sl: 100, extension: 200 }
  };

  return pipsConfig[par]?.[tipo] || 20;
};

export const analizarConGoogleVision = async (req, res) => {
  try {
    const filePath = req.file.path;

    console.log('🔍 Iniciando análisis con Google Vision OCR...');

    // Usar Google Vision para extraer texto
    const [result] = await client.textDetection(filePath);
    const detections = result.textAnnotations;
    const texto = detections[0]?.description || '';

    console.log("🧠 OCR por Google Vision:", texto.substring(0, 200) + '...');

    // Detectar par de divisas
    const parDetectado = detectarPar(texto);

    if (!parDetectado) {
      fs.unlinkSync(filePath);
      return res.status(400).json({
        mensaje: 'No se detectó ningún par de divisas válido en la imagen',
        textoDetectado: texto.substring(0, 100)
      });
    }

    console.log(`✅ Par detectado: ${parDetectado}`);

    // Limpiar texto para análisis
    const textoLimpio = texto.replace(/[^\d.,\s]/g, ' ').replace(/\s+/g, ' ').trim();
    console.log(`🧹 Texto limpio: ${textoLimpio}`);

    // Extraer precios específicos para el par detectado
    const precios = extraerPrecios(texto, parDetectado);

    if (precios.length === 0) {
      console.log(`❌ No se encontraron precios válidos para ${parDetectado}`);

      fs.unlinkSync(filePath);
      return res.status(400).json({
        mensaje: `Se detectó ${parDetectado} pero no se encontraron precios válidos.`,
        par: parDetectado,
        preciosDetectados: [],
        debug: {
          textoDetectado: texto.substring(0, 200),
          textoLimpio: textoLimpio,
          config: PARES_CONFIG[parDetectado]
        }
      });
    }

    console.log(`✅ Precios extraídos: ${precios.length} precios encontrados`);

    // Si solo tenemos 1 precio, generar algunos sintéticos para el análisis
    if (precios.length === 1) {
      const precio = precios[0];
      const variacion = precio * 0.001; // 0.1% de variación

      precios.push(precio - variacion);
      precios.push(precio + variacion);
      precios.sort((a, b) => a - b);

      console.log(`⚠️ Solo 1 precio encontrado, generando precios sintéticos: ${precios.join(', ')}`);
    }

    // Aplicar estrategia RunningPips REAL
    const analisis = aplicarEstrategiaRunningPips(parDetectado, precios);

    // Limpiar archivo
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      mensaje: 'Análisis completado con éxito',
      par: parDetectado,
      ...analisis,
      preciosDetectados: precios,
      totalPrecios: precios.length,
      debug: {
        textoDetectado: texto.substring(0, 200),
        textoLimpio: textoLimpio,
        config: PARES_CONFIG[parDetectado]
      }
    });

  } catch (error) {
    console.error('❌ Error usando Google Vision:', error);

    // Limpiar archivo en caso de error
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      mensaje: 'Error procesando imagen con Google Vision OCR.',
      error: error.message
    });
  }
};
