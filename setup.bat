@echo off
echo 🚀 Configurando AnalizadorGraficoApp - RunningPips
echo.

echo 📦 Instalando dependencias del Frontend...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Error instalando dependencias del frontend
    pause
    exit /b 1
)

echo.
echo 📦 Instalando dependencias del Backend...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Error instalando dependencias del backend
    pause
    exit /b 1
)
cd ..

echo.
echo 🔧 Verificando configuración...

REM Verificar si existe google-key.json
if exist "backend\google-key.json" (
    echo ✅ Google Cloud Vision configurado
) else (
    echo ⚠️ Google Cloud Vision no configurado (opcional)
    echo    La app funcionará con Tesseract.js
)

REM Verificar configuración de IP
echo.
echo 📡 Configuración de red:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set ip=%%a
    set ip=!ip: =!
    echo    Tu IP: !ip!
)

echo.
echo ✅ Configuración completada!
echo.
echo 📋 Próximos pasos:
echo 1. Actualizar IP en config/api.js si es necesario
echo 2. Ejecutar start-dev.bat para iniciar la aplicación
echo 3. O ejecutar manualmente:
echo    - Backend: cd backend ^&^& npm start
echo    - Frontend: npx expo start
echo.
echo Presiona cualquier tecla para continuar...
pause > nul
