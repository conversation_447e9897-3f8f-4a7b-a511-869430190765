// Script para probar OCR con texto real de gráficos
import { detectarPar, extraerPrecios, cleanOCRText } from './backend/controllers/ocrController.js';
import { getConfigForPair } from './backend/config/ocrConfig.js';

// Simular textos reales que el OCR podría detectar de gráficos
const textosRealesOCR = [
  {
    nombre: 'TradingView XAUUSD',
    texto: `XAUUSD 3.358,125 & +192% 1
    1987.65 1985.30 1990.45 1992.10
    H4 Chart Gold/USD`,
    esperado: 'XAUUSD'
  },
  {
    nombre: 'MetaTrader EURUSD',
    texto: `EUR/USD 1.12345
    1.12180 1.12567 1.12890
    M15 Euro vs US Dollar`,
    esperado: 'EURUSD'
  },
  {
    nombre: 'Gráfico con ruido',
    texto: `@ arrry @ smo [0 4 dcadores - 83 (@) Alerta 0 Reproduceion
    GBPJPY 156.789 155.234 158.456
    Ko +..:_ﬁﬁ¢qﬁ*+ lt@q,,.ilf 4'i+l o Hlp#ﬁ`,
    esperado: 'GBPJPY'
  },
  {
    nombre: 'Texto muy ruidoso',
    texto: `T S AA T e
    W e »
    i .| cBPUSEYS :
    ] l" ' \" "I*' 'T l'"ﬁ\\ esterITina/Défq%tadounidense :
    o RO I ! ,~¢ it
    B 'N Wl lil| |' i .`,
    esperado: null
  },
  {
    nombre: 'SPX500 con separadores',
    texto: `SPX500USD 5.801,8 ¥ -0.89%
    5801.8 5795.2 5810.5
    S&P 500 Index`,
    esperado: 'SPX500'
  }
];

console.log('🧪 Probando OCR mejorado con textos reales...\n');

for (const [index, prueba] of textosRealesOCR.entries()) {
  console.log(`\n📊 Prueba ${index + 1}: ${prueba.nombre}`);
  console.log('─'.repeat(50));
  console.log(`📝 Texto original:`);
  console.log(prueba.texto);
  
  // Limpiar texto
  const textoLimpio = cleanOCRText(prueba.texto);
  console.log(`\n🧹 Texto limpio:`);
  console.log(textoLimpio);
  
  // Detectar par
  console.log(`\n🔍 Detectando par...`);
  const parDetectado = detectarPar(prueba.texto);
  console.log(`🎯 Par detectado: ${parDetectado || 'NINGUNO'}`);
  console.log(`✅ Par esperado: ${prueba.esperado || 'NINGUNO'}`);
  
  if (parDetectado === prueba.esperado) {
    console.log('✅ ¡Detección correcta!');
  } else {
    console.log('❌ Detección incorrecta');
  }
  
  // Si se detectó un par, extraer precios
  if (parDetectado) {
    console.log(`\n💰 Extrayendo precios para ${parDetectado}...`);
    const config = getConfigForPair(parDetectado);
    console.log(`📋 Configuración: min=${config.minPrice}, max=${config.maxPrice}, decimales=${config.decimals}`);
    
    const precios = extraerPrecios(prueba.texto, parDetectado);
    console.log(`💰 Precios encontrados: ${precios.length > 0 ? precios.join(', ') : 'NINGUNO'}`);
    
    if (precios.length > 0) {
      console.log('✅ ¡Precios extraídos correctamente!');
      
      // Simular análisis básico
      const precioMin = Math.min(...precios);
      const precioMax = Math.max(...precios);
      const rango = precioMax - precioMin;
      const volatilidad = (rango / precioMin * 100).toFixed(2);
      
      console.log(`📊 Análisis básico:`);
      console.log(`   Mínimo: ${precioMin}`);
      console.log(`   Máximo: ${precioMax}`);
      console.log(`   Rango: ${rango.toFixed(config.decimals)}`);
      console.log(`   Volatilidad: ${volatilidad}%`);
    } else {
      console.log('❌ No se pudieron extraer precios');
    }
  }
  
  console.log('\n' + '═'.repeat(50));
}

console.log('\n🏁 Pruebas completadas');

// Función para probar patrones específicos
const probarPatrones = () => {
  console.log('\n🔍 Probando patrones de precios...\n');
  
  const textosPatrones = [
    '1987.65',      // XAUUSD normal
    '1,987.65',     // Con separador de miles
    '1987,65',      // Coma decimal europea
    '1.12345',      // EURUSD
    '156.789',      // GBPJPY
    '45678.90',     // BTCUSD
    '5801.8',       // SPX500
    '15,678.90',    // Con separador
    '1 987.65',     // Con espacio
    '1987',         // Sin decimales
  ];
  
  for (const texto of textosPatrones) {
    console.log(`📝 Probando: "${texto}"`);
    
    // Probar con diferentes pares
    const pares = ['XAUUSD', 'EURUSD', 'GBPJPY', 'BTCUSD', 'SPX500'];
    
    for (const par of pares) {
      const precios = extraerPrecios(texto, par);
      if (precios.length > 0) {
        console.log(`   ✅ ${par}: ${precios.join(', ')}`);
      }
    }
    console.log('');
  }
};

probarPatrones();

console.log('\n📋 Resumen de mejoras implementadas:');
console.log('✅ Limpieza avanzada de texto OCR');
console.log('✅ Detección de pares con variaciones');
console.log('✅ Patrones de precios más flexibles');
console.log('✅ Manejo de diferentes formatos numéricos');
console.log('✅ Logs detallados para debugging');
console.log('✅ Rangos de precios más permisivos');
console.log('✅ Fallback a patrones generales');
console.log('\n🎯 La aplicación ahora debería detectar mejor los gráficos reales!');
