import express from 'express';
import cors from 'cors';
import ip from 'ip';
import fs from 'fs';
import path from 'path';
import ocrRoutes from './routes/ocrRoute.js';

const app = express();
const PORT = process.env.PORT || 5000;

// Crear directorio uploads si no existe
const uploadsDir = path.join(process.cwd(), 'backend', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('📁 Directorio uploads creado');
}

// Configuración de CORS más permisiva para desarrollo
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use('/uploads', express.static('uploads'));

// Ruta de prueba
app.get('/', (_, res) => {
  res.json({
    mensaje: '🧠 Servidor RunningPips OCR funcionando',
    version: '2.0.0',
    endpoints: ['/api/ocr'],
    timestamp: new Date().toISOString()
  });
});

app.use('/api/ocr', ocrRoutes);

// Manejo de errores global
app.use((error, _, res, __) => {
  console.error('❌ Error del servidor:', error);
  res.status(500).json({
    mensaje: 'Error interno del servidor',
    error: error.message
  });
});

app.listen(PORT, '0.0.0.0', () => {
  const serverIP = ip.address();
  console.log(`🚀 Servidor RunningPips OCR iniciado`);
  console.log(`📡 URL: http://${serverIP}:${PORT}`);
  console.log(`📁 Uploads: ${uploadsDir}`);
  console.log(`⏰ Iniciado: ${new Date().toLocaleString()}`);
});
