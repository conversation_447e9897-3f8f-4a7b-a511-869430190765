import express from 'express';
import cors from 'cors';
import ocrRoutes from './routes/ocrRoute.js'; // ruta al archivo correcto

const app = express();
const PORT = process.env.PORT || 5000;

app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

app.use('/api/ocr', ocrRoutes); // Aquí se activa la ruta POST

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🧠 Servidor OCR corriendo en http://${require('ip').address()}:${PORT}`);
});
