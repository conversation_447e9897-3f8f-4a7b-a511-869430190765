# 🚀 Inicio Rápido - AnalizadorGraficoApp

## ⚡ Pasos para Iniciar (5 minutos)

### 1. 📦 Instalar Dependencias
```bash
# Ejecutar script automático
setup.bat

# O manualmente:
npm install
cd backend && npm install && cd ..
```

### 2. 🔧 Configurar IP del Servidor
1. Abrir `config/api.js`
2. Cambiar la IP en línea 9:
   ```javascript
   BASE_URL: 'http://TU_IP_AQUI:5000'
   ```
3. Para encontrar tu IP:
   - **Windows**: `ipconfig` (buscar "Dirección IPv4")
   - **Mac/Linux**: `ifconfig` (buscar "inet")

### 3. 🚀 Iniciar Aplicación
```bash
# Opción 1: Script automático
start-dev.bat

# Opción 2: Manual
# Terminal 1 (Backend):
cd backend
npm start

# Terminal 2 (Frontend):
npx expo start
```

### 4. 📱 Conectar Dispositivo
1. Instalar **Expo Go** en tu móvil
2. Escanear código QR que aparece en terminal
3. ¡Listo para usar!

---

## 🎯 Cómo Usar la App

### 📸 Tomar Foto de Gráfico
1. Abrir la app en tu móvil
2. Verificar que muestre "🟢 Servidor conectado"
3. Presionar "📷 Tomar Foto del Gráfico"
4. Enfocar el gráfico claramente
5. Asegurar que se vea:
   - **Par de divisas** (ej: XAUUSD, EURUSD)
   - **Precios** claramente legibles
6. Tomar la foto
7. Esperar análisis automático

### 🖼️ Subir desde Galería
1. Presionar "🖼️ Subir desde Galería"
2. Seleccionar imagen del gráfico
3. Esperar análisis

### 📊 Interpretar Resultados
La app mostrará:
- **Par detectado**: XAUUSD, EURUSD, etc.
- **Dirección**: BUY/SELL
- **Precio de entrada**: Nivel calculado
- **Stop Loss**: Nivel de protección
- **Take Profits**: TP1, TP2, TP3
- **Justificación**: Explicación del análisis

---

## ⚠️ Problemas Comunes

### 🔴 "Servidor desconectado"
**Solución:**
1. Verificar que backend esté ejecutándose
2. Actualizar IP en `config/api.js`
3. Reiniciar la app

### ❌ "No se detectó par de divisas"
**Solución:**
1. Asegurar que el par esté claramente visible
2. Usar imagen de alta calidad
3. Probar con pares soportados:
   - XAUUSD, EURUSD, GBPUSD, USDJPY
   - GBPJPY, BTCUSD, ETHUSD
   - SPX500, NAS100, US30

### 🔍 "No se detectaron precios"
**Solución:**
1. Verificar que los precios sean legibles
2. Evitar gráficos muy pequeños
3. Asegurar buen contraste
4. Revisar logs del servidor para debug

### 📱 App no carga en móvil
**Solución:**
1. Verificar que móvil y PC estén en misma WiFi
2. Usar túnel: `npx expo start --tunnel`
3. Actualizar Expo Go en el móvil

---

## 🧪 Probar Funcionamiento

### Texto de Prueba
Crear imagen simple con texto:
```
XAUUSD
1987.65
1985.30
1990.45
```

### Verificar Logs
En la consola del backend deberías ver:
```
✅ Par detectado exacto: XAUUSD
🔍 Precios encontrados: 1985.30, 1987.65, 1990.45
✅ Análisis completado
```

---

## 📋 Checklist de Verificación

- [ ] ✅ Backend iniciado (puerto 5000)
- [ ] ✅ Frontend iniciado (Expo)
- [ ] ✅ IP configurada correctamente
- [ ] ✅ Móvil conectado a misma WiFi
- [ ] ✅ Expo Go instalado
- [ ] ✅ App muestra "Servidor conectado"
- [ ] ✅ Permisos de cámara otorgados

---

## 🆘 Obtener Ayuda

Si algo no funciona:

1. **Revisar logs** en consola del backend
2. **Verificar IP** en `config/api.js`
3. **Probar con imagen simple** de prueba
4. **Consultar** `TROUBLESHOOTING.md`

---

## 🎉 ¡Listo!

Tu aplicación de análisis de gráficos con estrategia RunningPips está funcionando.

**Recuerda:**
- Solo para fines educativos
- No constituye asesoría financiera
- Gestiona el riesgo adecuadamente

¡Disfruta analizando gráficos! 📊✨
