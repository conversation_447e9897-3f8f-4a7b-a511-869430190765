const candidatos = texto.toUpperCase().match(/[A-Z]{4,6}/g) || [];
let mejorPar = 'No detectado';
let menorDistancia = 3;

for (const palabra of candidatos) {
  for (const par of paresPermitidos) {
    const dist = distancia(palabra, par);
    if (dist < menorDistancia) {
      menorDistancia = dist;
      mejorPar = par;
    }
  }
}
if (mejorPar === 'No detectado') {
  return res.json({ mensaje: 'No se detectó el par en el gráfico.' });
}
