# 📊 AnalizadorGraficoApp - RunningPips Strategy

Una aplicación móvil desarrollada con React Native + Expo que utiliza OCR (Reconocimiento Óptico de Caracteres) para analizar gráficos de trading en tiempo real y aplicar la estrategia institucional RunningPips.

## 🚀 Características

- **OCR Avanzado**: Utiliza Google Cloud Vision API y Tesseract.js como fallback
- **Estrategia RunningPips**: Implementa análisis institucional automático
- **Detección Inteligente**: Reconoce 14 pares de divisas y criptomonedas
- **Análisis en Tiempo Real**: Procesa imágenes desde cámara o galería
- **Cálculos Precisos**: SL y TPs basados en estructura de mercado

## 📱 Pares Soportados

```
FOREX: XAUUSD, GBPJPY, EURUSD, USDJPY, AUDUSD, NZDUSD, USDCAD, EURJPY, GBPUSD
CRYPTO: BTCUSD, ETHUSD
ÍNDICES: SPX500, NAS100, US30
```

## 🛠️ Instalación

### Prerrequisitos
- Node.js 18+
- Expo CLI
- Android Studio / Xcode (para emuladores)

### Backend
```bash
cd backend
npm install
npm start
```

### Frontend
```bash
npm install
npx expo start
```

**Nota**: Si tienes la CLI global de Expo instalada, usa `npx expo start` en lugar de `expo start` para evitar problemas de compatibilidad.

## 📋 Configuración

1. **Google Cloud Vision (Opcional)**:
   - Crear proyecto en Google Cloud Console
   - Habilitar Vision API
   - Descargar `google-key.json` y colocar en `/backend/`

2. **IP del Servidor**:
   - Actualizar IP en `screens/MainScreen.js` línea 32
   - Usar la IP mostrada al iniciar el backend

## 🧠 Estrategia RunningPips

La aplicación implementa los siguientes conceptos:

- **BOS (Break of Structure)**: Detección de rompimientos
- **Liquidez Institucional**: Identificación de zonas de oferta/demanda
- **Order Blocks**: Análisis de bloques de órdenes
- **FVG (Fair Value Gaps)**: Detección de gaps de valor justo

## 📊 Análisis Automático

1. **Detección de Par**: Reconoce el instrumento en la imagen
2. **Extracción de Precios**: Identifica niveles relevantes
3. **Análisis de Estructura**: Determina dirección del mercado
4. **Cálculo de Niveles**: SL y TPs basados en volatilidad
5. **Justificación**: Explica la lógica del análisis

## 🔧 Uso

1. Abrir la aplicación
2. Tomar foto del gráfico o seleccionar desde galería
3. Esperar análisis automático
4. Revisar resultados y niveles calculados

## ⚠️ Advertencias

- **Solo para fines educativos**
- **No constituye asesoría financiera**
- **El usuario es responsable de sus decisiones de trading**
- **Gestión de riesgo recomendada: 0.01 lotes por cada $100**

## 🏗️ Arquitectura

```
AnalizadorGraficoApp/
├── backend/
│   ├── controllers/ocrController.js    # Lógica OCR y estrategia
│   ├── routes/ocrRoute.js             # Rutas API
│   ├── server.js                      # Servidor Express
│   └── uploads/                       # Imágenes temporales
├── screens/
│   ├── MainScreen.js                  # Pantalla principal
│   └── TerminosScreen.js             # Términos y condiciones
├── assets/                           # Recursos gráficos
└── App.js                           # Componente raíz
```

## 🔄 Flujo de Datos

1. **Frontend** → Captura imagen
2. **API** → Recibe imagen via FormData
3. **OCR** → Extrae texto (Google Vision/Tesseract)
4. **Análisis** → Aplica estrategia RunningPips
5. **Respuesta** → Devuelve niveles calculados
6. **UI** → Muestra resultados formateados

## 🐛 Troubleshooting

### Problemas Comunes

**No detecta el par:**
- Verificar que el par esté claramente visible
- Asegurar buena calidad de imagen
- Revisar logs del servidor

**Error de conexión:**
- Verificar IP del servidor
- Confirmar que backend esté ejecutándose
- Revisar configuración de CORS

**OCR no funciona:**
- Google Vision requiere `google-key.json`
- Tesseract funciona como fallback
- Verificar permisos de cámara/galería

## 📈 Mejoras Futuras

- [ ] Soporte para más pares de divisas
- [ ] Análisis de múltiples timeframes
- [ ] Integración con brokers
- [ ] Alertas push
- [ ] Historial de análisis
- [ ] Backtesting automático

## 👨‍💻 Desarrollo

Desarrollado por **Germayori Corp** para la comunidad de trading.

**Versión**: 2.0.0
**Licencia**: Uso educativo
**Soporte**: Comunidad RunningPips

---

*"El trading institucional al alcance de todos"* 📊✨
