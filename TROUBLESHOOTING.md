# 🔧 Troubleshooting - AnalizadorGraficoApp

## Problemas Comunes y Soluciones

### 1. Error de Expo CLI

**Problema:**
```
ExpoMetroConfig.loadAsync is not a function
WARNING: The legacy expo-cli does not support Node +17
```

**Solución:**
```bash
# Usar la nueva CLI local en lugar de la global
npx expo start

# O actualizar scripts en package.json
npm run start
```

### 2. Error de Conexión con Backend

**Problema:**
- App muestra "🔴 Servidor desconectado"
- Error: "No se puede conectar con el servidor"

**Solución:**
1. Verificar que el backend esté ejecutándose:
   ```bash
   cd backend
   npm start
   ```

2. Actualizar IP en `config/api.js`:
   ```javascript
   BASE_URL: 'http://TU_IP_AQUI:5000'
   ```

3. Encontrar tu IP:
   ```bash
   # Windows
   ipconfig
   
   # Mac/Linux
   ifconfig
   ```

### 3. OCR No Detecta Texto

**Problema:**
- No se detectan pares de divisas
- No se extraen precios

**Solución:**
1. **Calidad de imagen:**
   - Usar imágenes de alta resolución
   - Asegurar buen contraste
   - Evitar imágenes borrosas

2. **Verificar logs del servidor:**
   ```
   🧠 TEXTO DETECTADO: [verificar que aparezca texto]
   ```

3. **Probar con imagen de prueba:**
   - Crear imagen simple con "XAUUSD 1987.65"
   - Verificar que funcione el OCR básico

### 4. Permisos de Cámara/Galería

**Problema:**
- No se puede acceder a cámara
- No se puede seleccionar desde galería

**Solución:**
1. **Android:**
   - Ir a Configuración > Apps > Expo Go > Permisos
   - Habilitar Cámara y Almacenamiento

2. **iOS:**
   - Ir a Configuración > Privacidad > Cámara
   - Habilitar para Expo Go

### 5. Error de Instalación de Dependencias

**Problema:**
```
npm ERR! peer dep missing
```

**Solución:**
```bash
# Limpiar cache y reinstalar
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# Para backend
cd backend
rm -rf node_modules package-lock.json
npm install
```

### 6. Google Cloud Vision No Funciona

**Problema:**
- Error: "Google Vision no disponible"
- Credenciales inválidas

**Solución:**
1. **Verificar archivo de credenciales:**
   - Colocar `google-key.json` en `/backend/`
   - Verificar que el archivo sea válido JSON

2. **Configurar proyecto en Google Cloud:**
   - Habilitar Vision API
   - Crear service account
   - Descargar credenciales

3. **Fallback a Tesseract:**
   - La app funciona sin Google Vision
   - Tesseract se usa automáticamente

### 7. Problemas de Red/CORS

**Problema:**
- Error de CORS
- Network request failed

**Solución:**
1. **Verificar configuración CORS en backend:**
   ```javascript
   app.use(cors({
     origin: '*',
     methods: ['GET', 'POST']
   }));
   ```

2. **Verificar firewall:**
   - Permitir puerto 5000
   - Verificar que no esté bloqueado

### 8. App No Inicia en Dispositivo

**Problema:**
- QR code no funciona
- App no carga en dispositivo

**Solución:**
1. **Verificar conexión de red:**
   - Dispositivo y PC en la misma red WiFi
   - Verificar IP de la PC

2. **Usar túnel:**
   ```bash
   npx expo start --tunnel
   ```

3. **Verificar Expo Go:**
   - Actualizar Expo Go en el dispositivo
   - Reiniciar la app

### 9. Análisis Incorrecto

**Problema:**
- Precios detectados incorrectos
- SL/TP no realistas

**Solución:**
1. **Verificar imagen:**
   - Par de divisas claramente visible
   - Precios legibles y sin obstáculos

2. **Revisar configuración de pares:**
   - Verificar rangos en `ocrConfig.js`
   - Ajustar patrones si es necesario

3. **Debug info:**
   - Revisar información de debug en la app
   - Verificar texto detectado por OCR

### 10. Performance Lento

**Problema:**
- OCR toma mucho tiempo
- App se congela

**Solución:**
1. **Optimizar imágenes:**
   - Reducir resolución si es muy alta
   - Usar formato JPEG

2. **Verificar recursos:**
   - Cerrar otras aplicaciones
   - Verificar memoria disponible

## 📞 Obtener Ayuda

Si ninguna solución funciona:

1. **Revisar logs:**
   - Backend: Consola del servidor
   - Frontend: Expo Dev Tools

2. **Información útil para reportar:**
   - Versión de Node.js: `node --version`
   - Versión de Expo: `npx expo --version`
   - Sistema operativo
   - Mensaje de error completo
   - Pasos para reproducir el problema

3. **Archivos de log:**
   - Logs del servidor backend
   - Logs de Expo Dev Tools
   - Screenshots del error

## 🔄 Reset Completo

Si todo falla, reset completo:

```bash
# 1. Limpiar dependencias
rm -rf node_modules package-lock.json
cd backend
rm -rf node_modules package-lock.json
cd ..

# 2. Reinstalar todo
npm install
cd backend
npm install
cd ..

# 3. Limpiar cache de Expo
npx expo start --clear

# 4. Reiniciar servidor
cd backend
npm start
```
