import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { analizarImagenOCR } from '../controllers/ocrController.js';

const router = express.Router();

// Asegurar que el directorio uploads existe
const uploadsPath = path.join(process.cwd(), 'backend', 'uploads');
if (!fs.existsSync(uploadsPath)) {
  fs.mkdirSync(uploadsPath, { recursive: true });
}

const storage = multer.diskStorage({
  destination: (_, __, cb) => {
    cb(null, uploadsPath);
  },
  filename: (_, file, cb) => {
    const timestamp = Date.now();
    const extension = path.extname(file.originalname);
    const filename = `grafico-${timestamp}${extension}`;
    cb(null, filename);
  }
});

// Configuración de multer con validaciones
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB máximo
  },
  fileFilter: (_, file, cb) => {
    // Validar tipos de archivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de archivo no permitido. Solo JPEG, PNG y WebP.'), false);
    }
  }
});

// Middleware para manejo de errores de multer
const handleMulterError = (error, _, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ mensaje: 'Archivo demasiado grande. Máximo 10MB.' });
    }
  }
  if (error.message.includes('Tipo de archivo no permitido')) {
    return res.status(400).json({ mensaje: error.message });
  }
  next(error);
};

router.post('/', upload.single('imagen'), handleMulterError, analizarImagenOCR);

// Ruta de prueba
router.get('/test', (_, res) => {
  res.json({
    mensaje: 'Endpoint OCR funcionando',
    timestamp: new Date().toISOString(),
    uploadsPath
  });
});

export default router;
