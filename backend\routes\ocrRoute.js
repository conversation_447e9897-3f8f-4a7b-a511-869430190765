import express from 'express';
import multer from 'multer';
import { analizarImagenOCR } from '../controllers/ocrController.js';

const router = express.Router();

const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, 'uploads/'),
  filename: (req, file, cb) => cb(null, `${Date.now()}-${file.originalname}`)
});
const upload = multer({ storage });

router.post('/', upload.single('imagen'), analizarImagenOCR);

export default router;
