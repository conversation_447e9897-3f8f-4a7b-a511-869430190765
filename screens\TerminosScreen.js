// /screens/TerminosScreen.js
import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

export default function TerminosScreen() {
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>📄 Términos y Condiciones</Text>

      <Text style={styles.sectionTitle}>⚠️ Advertencia de Riesgo:</Text>
      <Text style={styles.text}>
        Esta aplicación ofrece análisis educativos basados en la estrategia institucional RunningPips.
        El usuario es el único responsable del uso de los datos proporcionados.
      </Text>

      <Text style={styles.sectionTitle}>📌 Recomendaciones de Gestión de Riesgo:</Text>
      <Text style={styles.text}>
        Por cada $100 de capital, se recomienda operar con un máximo de 0.01 lotes.
        No se recomienda el sobre-apalancamiento o el uso de grandes volúmenes en cuentas pequeñas.
      </Text>

      <Text style={styles.sectionTitle}>🔒 Responsabilidad Legal:</Text>
      <Text style={styles.text}>
        Germayori Corp. y esta aplicación no se hacen responsables por pérdidas ocasionadas por el mal uso de la herramienta.
        Esta app es con fines educativos y no constituye asesoría financiera.
      </Text>

      <Text style={styles.sectionTitle}>📘 Parámetros PRO - RunningPips:</Text>
      <Text style={styles.text}>
        • Esperar liquidez en alto o bajo{"\n"}
        • Confirmar rompimiento de estructura (BOS){"\n"}
        • Entrada en zona FVG u Order Block{"\n"}
        • SL: en el alto o bajo anterior{"\n"}
        • TP: en la zona opuesta de liquidez{"\n"}
        • Reentrada si la estructura permanece{"\n"}
      </Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#0A0F1C',
  },
  title: {
    color: '#00FFC6',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  sectionTitle: {
    color: '#1E90FF',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
  },
  text: {
    color: '#fff',
    fontSize: 15,
    lineHeight: 22,
  },
});
