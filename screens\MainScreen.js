import React, { useState, useEffect } from 'react';
import {
  View, Text, TouchableOpacity, Image, StyleSheet, ScrollView, ImageBackground
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from '@react-navigation/native';

export default function MainScreen() {
  const [image, setImage] = useState(null);
  const [resultado, setResultado] = useState(null);
  const navigation = useNavigation();

  useEffect(() => {
    (async () => {
      const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
      const mediaStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (cameraStatus.status !== 'granted' || mediaStatus.status !== 'granted') {
        alert('Se requiere permiso de cámara y galería.');
      }
    })();
  }, []);

  const analizarFoto = async (uri) => {
    try {
      const formData = new FormData();
      formData.append('imagen', {
        uri: uri,
        type: 'image/jpeg',
        name: 'grafico.jpg',
      });

      const response = await fetch('http://***********:5000/api/ocr', {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = await response.json();

      if (response.ok && data?.precioEntrada && data?.sl && data?.tp1 && data?.tp2 && data?.tp3) {
        setResultado({
          entrada: 'BUY',
          par: data.par || '-',
          precio: data.precioEntrada?.toFixed(2),
          sl: `SL: ${data.sl?.toFixed(2)}`,
          tp: `TP1: ${data.tp1?.toFixed(2)} | TP2: ${data.tp2?.toFixed(2)} | TP3: ${data.tp3?.toFixed(2)}`,
          justificacion: `📊 Entrada institucional detectada en zona ${data.precioEntrada?.toFixed(2)} con SL ${data.sl?.toFixed(2)} y TPs escalonados.`,
        });
      } else {
        setResultado({
          entrada: '❌ No válida',
          par: data?.par || '-',
          precio: '-',
          sl: '-',
          tp: '-',
          justificacion: data?.mensaje || 'No se detectaron precios suficientes.',
        });
      }
    } catch (error) {
      console.error('Error:', error);
      setResultado({
        entrada: '❌ Error',
        par: '-',
        precio: '-',
        sl: '-',
        tp: '-',
        justificacion: 'No se pudo conectar con el backend.',
      });
    }
  };

  const tomarFoto = async () => {
    let result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      quality: 0.8,
    });

    if (!result.canceled) {
      const uri = result.assets[0].uri;
      setImage(uri);
      analizarFoto(uri);
    }
  };

  const seleccionarDesdeGaleria = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      quality: 0.8,
    });

    if (!result.canceled) {
      const uri = result.assets[0].uri;
      setImage(uri);
      analizarFoto(uri);
    }
  };

  return (
    <ImageBackground
      source={require('../assets/trading.png')}
      style={styles.background}
      resizeMode="cover"
    >
      <ScrollView contentContainerStyle={styles.container}>

        <View style={styles.aviso}>
          <Text style={styles.avisoTitle}>📋 Pares Disponibles para Análisis</Text>
          <Text style={styles.avisoText}>
            XAUUSD | GBPJPY | EURUSD | USDJPY | BTCUSD{"\n"}
            ETHUSD | AUDUSD | NZDUSD | USDCAD | EURJPY{"\n"}
            GBPUSD | SPX500 | NAS100 | US30
          </Text>
          <Text style={styles.avisoNote}>
            🔔 Asegúrate de que el par esté bien visible y sin espacios. Ej: “XAUUSD”.
          </Text>
        </View>

        <Text style={styles.title}>📸 Análisis de Gráfico - RunningPips</Text>

        <TouchableOpacity style={styles.button} onPress={tomarFoto}>
          <Text style={styles.buttonText}>📷 Tomar Foto del Gráfico</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={seleccionarDesdeGaleria}>
          <Text style={styles.buttonText}>🖼️ Subir desde Galería</Text>
        </TouchableOpacity>

        {image && <Image source={{ uri: image }} style={styles.preview} />}

        {resultado && (
          <View style={styles.resultado}>
            <Text style={styles.resultadoText}>📊 Par: {resultado.par}</Text>
            <Text style={styles.resultadoText}>🔎 Entrada: {resultado.entrada}</Text>
            <Text style={styles.resultadoText}>💰 Precio: {resultado.precio}</Text>
            <Text style={styles.resultadoText}>📍 {resultado.sl}</Text>
            <Text style={styles.resultadoText}>🎯 {resultado.tp}</Text>
            <Text style={styles.resultadoText}>🧠 {resultado.justificacion}</Text>
          </View>
        )}

        <TouchableOpacity onPress={() => navigation.navigate('Términos')}>
          <Text style={styles.linkText}>📄 Ver Términos y Condiciones</Text>
        </TouchableOpacity>
      </ScrollView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
  },
  container: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  aviso: {
    backgroundColor: '#111827',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    width: '100%',
  },
  avisoTitle: {
    color: '#00FFC6',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    textAlign: 'center',
  },
  avisoText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontSize: 14,
  },
  avisoNote: {
    color: '#FBBF24',
    fontSize: 13,
    marginTop: 8,
    textAlign: 'center',
  },
  title: {
    color: '#00FFC6',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 10,
    borderRadius: 10,
  },
  button: {
    backgroundColor: '#1E88E5',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  preview: {
    width: 250,
    height: 250,
    borderRadius: 10,
    marginBottom: 20,
  },
  resultado: {
    backgroundColor: '#1F2937',
    padding: 15,
    borderRadius: 10,
    width: '100%',
    marginBottom: 30,
  },
  resultadoText: {
    color: '#FFFFFF',
    marginBottom: 5,
  },
  linkText: {
    color: '#00FFC6',
    marginTop: 25,
    fontSize: 16,
    textDecorationLine: 'underline',
  },
});
