import React, { useState, useEffect } from 'react';
import {
  View, Text, TouchableOpacity, Image, StyleSheet, ScrollView, ImageBackground, Alert
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from '@react-navigation/native';
import { sendImageToOCR, testConnection } from '../config/api.js';

export default function MainScreen() {
  const [image, setImage] = useState(null);
  const [resultado, setResultado] = useState(null);
  const [loading, setLoading] = useState(false);
  const [serverStatus, setServerStatus] = useState('checking');
  const navigation = useNavigation();

  useEffect(() => {
    (async () => {
      // Verificar permisos
      const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
      const mediaStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (cameraStatus.status !== 'granted' || mediaStatus.status !== 'granted') {
        Alert.alert('Permisos requeridos', 'Se requiere permiso de cámara y galería para usar la aplicación.');
      }

      // Verificar conexión con el servidor
      const connectionTest = await testConnection();
      if (connectionTest.success) {
        setServerStatus('connected');
        console.log('✅ Servidor conectado:', connectionTest.data);
      } else {
        setServerStatus('disconnected');
        console.log('❌ Error de conexión:', connectionTest.error);
      }
    })();
  }, []);

  const analizarFoto = async (uri) => {
    if (serverStatus !== 'connected') {
      Alert.alert('Error de conexión', 'No se puede conectar con el servidor. Verifica que esté ejecutándose.');
      return;
    }

    setLoading(true);
    setResultado(null);

    try {
      const result = await sendImageToOCR(uri);
      console.log('📊 Respuesta del servidor:', result);

      if (result.success) {
        const data = result.data;

        if (data?.precioEntrada && data?.sl && data?.tp1 && data?.tp2 && data?.tp3) {
          setResultado({
            entrada: data.direccion || 'BUY',
            par: data.par || '-',
            precio: data.precioEntrada,
            sl: data.sl,
            tp1: data.tp1,
            tp2: data.tp2,
            tp3: data.tp3,
            justificacion: data.justificacion || 'Análisis completado',
            volatilidad: data.volatilidad,
            rango: data.rango,
            totalPrecios: data.totalPrecios,
            preciosDetectados: data.preciosDetectados
          });
        } else {
          setResultado({
            entrada: '❌ No válida',
            par: data?.par || '-',
            precio: '-',
            sl: '-',
            tp1: '-',
            tp2: '-',
            tp3: '-',
            justificacion: data?.mensaje || 'No se detectaron precios suficientes.',
            debug: {
              textoDetectado: data?.textoDetectado,
              preciosDetectados: data?.preciosDetectados,
              totalPrecios: data?.totalPrecios
            }
          });
        }
      } else {
        setResultado({
          entrada: '❌ Error',
          par: '-',
          precio: '-',
          sl: '-',
          tp1: '-',
          tp2: '-',
          tp3: '-',
          justificacion: `Error de servidor: ${result.error}`,
        });
      }
    } catch (error) {
      console.error('Error:', error);
      setResultado({
        entrada: '❌ Error',
        par: '-',
        precio: '-',
        sl: '-',
        tp1: '-',
        tp2: '-',
        tp3: '-',
        justificacion: 'Error de conexión con el servidor.',
      });
    } finally {
      setLoading(false);
    }
  };

  const tomarFoto = async () => {
    let result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      quality: 0.8,
    });

    if (!result.canceled) {
      const uri = result.assets[0].uri;
      setImage(uri);
      analizarFoto(uri);
    }
  };

  const seleccionarDesdeGaleria = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      quality: 0.8,
    });

    if (!result.canceled) {
      const uri = result.assets[0].uri;
      setImage(uri);
      analizarFoto(uri);
    }
  };

  return (
    <ImageBackground
      source={require('../assets/trading.png')}
      style={styles.background}
      resizeMode="cover"
    >
      <ScrollView contentContainerStyle={styles.container}>

        <View style={styles.aviso}>
          <Text style={styles.avisoTitle}>📋 Pares Disponibles para Análisis</Text>
          <Text style={styles.avisoText}>
            XAUUSD | GBPJPY | EURUSD | USDJPY | BTCUSD{"\n"}
            ETHUSD | AUDUSD | NZDUSD | USDCAD | EURJPY{"\n"}
            GBPUSD | SPX500 | NAS100 | US30
          </Text>
          <Text style={styles.avisoNote}>
            🔔 Asegúrate de que el par esté bien visible y sin espacios. Ej: “XAUUSD”.
          </Text>
        </View>

        <Text style={styles.title}>📸 Análisis de Gráfico - RunningPips</Text>

        {/* Indicador de estado del servidor */}
        <View style={styles.serverStatus}>
          <Text style={[styles.statusText,
            serverStatus === 'connected' ? styles.statusConnected :
            serverStatus === 'disconnected' ? styles.statusDisconnected :
            styles.statusChecking]}>
            {serverStatus === 'connected' ? '🟢 Servidor conectado' :
             serverStatus === 'disconnected' ? '🔴 Servidor desconectado' :
             '🟡 Verificando conexión...'}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.button, (loading || serverStatus !== 'connected') && styles.buttonDisabled]}
          onPress={tomarFoto}
          disabled={loading || serverStatus !== 'connected'}>
          <Text style={styles.buttonText}>
            {loading ? '⏳ Analizando...' : '📷 Tomar Foto del Gráfico'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, (loading || serverStatus !== 'connected') && styles.buttonDisabled]}
          onPress={seleccionarDesdeGaleria}
          disabled={loading || serverStatus !== 'connected'}>
          <Text style={styles.buttonText}>
            {loading ? '⏳ Analizando...' : '🖼️ Subir desde Galería'}
          </Text>
        </TouchableOpacity>

        {image && <Image source={{ uri: image }} style={styles.preview} />}

        {resultado && (
          <View style={styles.resultado}>
            <Text style={styles.resultadoTitle}>📊 Análisis RunningPips</Text>

            <View style={styles.resultadoRow}>
              <Text style={styles.resultadoLabel}>Par:</Text>
              <Text style={styles.resultadoValue}>{resultado.par}</Text>
            </View>

            <View style={styles.resultadoRow}>
              <Text style={styles.resultadoLabel}>Dirección:</Text>
              <Text style={[styles.resultadoValue,
                resultado.entrada === 'BUY' ? styles.buyColor :
                resultado.entrada === 'SELL' ? styles.sellColor : styles.errorColor]}>
                {resultado.entrada}
              </Text>
            </View>

            {resultado.precio !== '-' && (
              <>
                <View style={styles.resultadoRow}>
                  <Text style={styles.resultadoLabel}>Entrada:</Text>
                  <Text style={styles.resultadoValue}>{resultado.precio}</Text>
                </View>

                <View style={styles.resultadoRow}>
                  <Text style={styles.resultadoLabel}>Stop Loss:</Text>
                  <Text style={styles.resultadoValue}>{resultado.sl}</Text>
                </View>

                <View style={styles.tpContainer}>
                  <Text style={styles.resultadoLabel}>Take Profits:</Text>
                  <Text style={styles.tpText}>TP1: {resultado.tp1}</Text>
                  <Text style={styles.tpText}>TP2: {resultado.tp2}</Text>
                  <Text style={styles.tpText}>TP3: {resultado.tp3}</Text>
                </View>

                {resultado.volatilidad && (
                  <View style={styles.resultadoRow}>
                    <Text style={styles.resultadoLabel}>Volatilidad:</Text>
                    <Text style={styles.resultadoValue}>{resultado.volatilidad}%</Text>
                  </View>
                )}

                {resultado.totalPrecios && (
                  <View style={styles.resultadoRow}>
                    <Text style={styles.resultadoLabel}>Precios detectados:</Text>
                    <Text style={styles.resultadoValue}>{resultado.totalPrecios}</Text>
                  </View>
                )}
              </>
            )}

            <Text style={styles.justificacion}>{resultado.justificacion}</Text>

            {resultado.debug && (
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>🔍 Debug Info:</Text>
                {resultado.debug.textoDetectado && (
                  <Text style={styles.debugText}>
                    Texto: {resultado.debug.textoDetectado.substring(0, 100)}...
                  </Text>
                )}
                {resultado.debug.preciosDetectados && (
                  <Text style={styles.debugText}>
                    Precios: {resultado.debug.preciosDetectados.join(', ')}
                  </Text>
                )}
              </View>
            )}
          </View>
        )}

        <TouchableOpacity onPress={() => navigation.navigate('Términos')}>
          <Text style={styles.linkText}>📄 Ver Términos y Condiciones</Text>
        </TouchableOpacity>
      </ScrollView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
  },
  container: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  aviso: {
    backgroundColor: '#111827',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    width: '100%',
  },
  avisoTitle: {
    color: '#00FFC6',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    textAlign: 'center',
  },
  avisoText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontSize: 14,
  },
  avisoNote: {
    color: '#FBBF24',
    fontSize: 13,
    marginTop: 8,
    textAlign: 'center',
  },
  title: {
    color: '#00FFC6',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 10,
    borderRadius: 10,
  },
  serverStatus: {
    backgroundColor: '#111827',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  statusConnected: {
    color: '#10B981',
  },
  statusDisconnected: {
    color: '#EF4444',
  },
  statusChecking: {
    color: '#F59E0B',
  },
  button: {
    backgroundColor: '#1E88E5',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#6B7280',
    opacity: 0.6,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  preview: {
    width: 250,
    height: 250,
    borderRadius: 10,
    marginBottom: 20,
  },
  resultado: {
    backgroundColor: '#1F2937',
    padding: 20,
    borderRadius: 15,
    width: '100%',
    marginBottom: 30,
    borderWidth: 1,
    borderColor: '#374151',
  },
  resultadoTitle: {
    color: '#00FFC6',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  resultadoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultadoLabel: {
    color: '#9CA3AF',
    fontSize: 14,
    fontWeight: '500',
  },
  resultadoValue: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  buyColor: {
    color: '#10B981',
  },
  sellColor: {
    color: '#EF4444',
  },
  errorColor: {
    color: '#F59E0B',
  },
  tpContainer: {
    backgroundColor: '#111827',
    padding: 10,
    borderRadius: 8,
    marginVertical: 8,
  },
  tpText: {
    color: '#00FFC6',
    fontSize: 13,
    marginBottom: 3,
  },
  justificacion: {
    color: '#E5E7EB',
    fontSize: 13,
    fontStyle: 'italic',
    marginTop: 10,
    textAlign: 'center',
    lineHeight: 18,
  },
  debugContainer: {
    backgroundColor: '#0F172A',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  debugTitle: {
    color: '#F59E0B',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  debugText: {
    color: '#6B7280',
    fontSize: 11,
    marginBottom: 3,
  },
  linkText: {
    color: '#00FFC6',
    marginTop: 25,
    fontSize: 16,
    textDecorationLine: 'underline',
  },
});
