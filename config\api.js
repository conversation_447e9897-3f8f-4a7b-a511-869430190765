// Configuración de la API
export const API_CONFIG = {
  // Cambiar esta IP por la IP de tu computadora
  // Para encontrar tu IP: 
  // Windows: ipconfig
  // Mac/Linux: ifconfig
  BASE_URL: 'http://***********:5000',
  
  // Endpoints
  ENDPOINTS: {
    OCR: '/api/ocr',
    TEST: '/api/ocr/test',
    HEALTH: '/'
  },
  
  // Configuración de requests
  REQUEST_CONFIG: {
    timeout: 30000, // 30 segundos
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  }
};

// Función para obtener la URL completa de un endpoint
export const getApiUrl = (endpoint) => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Función para probar la conexión con el servidor
export const testConnection = async () => {
  try {
    const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.HEALTH));
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Función para enviar imagen al OCR
export const sendImageToOCR = async (imageUri) => {
  try {
    const formData = new FormData();
    formData.append('imagen', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'grafico.jpg',
    });

    const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.OCR), {
      method: 'POST',
      body: formData,
      headers: API_CONFIG.REQUEST_CONFIG.headers,
    });

    const data = await response.json();
    return { success: response.ok, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
};
