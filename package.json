{"name": "analizadorgraficoapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "expo": "~53.0.9", "expo-camera": "~16.1.6", "expo-file-system": "~18.1.10", "expo-image-picker": "~16.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "tesseract.js": "^6.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/ngrok": "^4.1.3"}, "private": true, "expo": {"platforms": ["ios", "android", "web"]}}