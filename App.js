import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import MainScreen from './screens/MainScreen';
import TerminosScreen from './screens/TerminosScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: true }}>
        <Stack.Screen name="Inicio" component={MainScreen} />
        <Stack.Screen name="Términos" component={TerminosScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
