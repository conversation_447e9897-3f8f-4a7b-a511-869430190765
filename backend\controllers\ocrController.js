import fs from 'fs';
import path from 'path';
import vision from '@google-cloud/vision';

const __dirname = path.resolve(); // Asegura la ruta absoluta

// Configuración del cliente de Google Vision
const client = new vision.ImageAnnotatorClient({
  keyFilename: path.join(__dirname, 'google-key.json') // ✅ Debe estar en /backend/
});

export const analizarImagenOCR = async (req, res) => {
  try {
    const imagePath = req.file.path;

    // OCR con Google Cloud Vision
    const [result] = await client.textDetection(imagePath);
    const detections = result.textAnnotations;
    const textoDetectado = detections.length > 0 ? detections[0].description : '';

    console.log('🧠 TEXTO DETECTADO:', textoDetectado);

    // Buscar par de divisas permitido
    const regexPar = /(XAUUSD|GBPJPY|EURUSD|USDJPY|BTCUSD|ETHUSD|AUDUSD|NZDUSD|USDCAD|EURJPY|GBPUSD|SPX500|NAS100|US30)/i;
    const parDetectado = textoDetectado.match(regexPar)?.[0]?.toUpperCase() || null;

    // Buscar precio en formato 1234.567 o 123.45
    const precios = textoDetectado.match(/\d{2,5}\.\d{1,5}/g)?.map(p => parseFloat(p)) || [];

    if (!parDetectado || precios.length < 1) {
      return res.json({
        mensaje: 'No se detectaron precios suficientes o par de divisas.',
        par: null,
        precioEntrada: null,
        sl: null,
        tp1: null,
        tp2: null,
        tp3: null
      });
    }

    const precioEntrada = precios[0];
    const sl = parseFloat((precioEntrada - 0.02).toFixed(2));
    const tp1 = parseFloat((precioEntrada + 0.02).toFixed(2));
    const tp2 = parseFloat((precioEntrada + 0.04).toFixed(2));
    const tp3 = parseFloat((precioEntrada + 0.06).toFixed(2));

    res.json({
      mensaje: 'Texto analizado con éxito',
      par: parDetectado,
      precioEntrada,
      sl,
      tp1,
      tp2,
      tp3
    });

  } catch (error) {
    console.error('❌ Error en OCR:', error);
    res.status(500).json({ mensaje: 'Error al analizar la imagen.', error: error.message });
  } finally {
    if (req.file && req.file.path) {
      fs.unlinkSync(req.file.path); // Borra la imagen temporal
    }
  }
};
