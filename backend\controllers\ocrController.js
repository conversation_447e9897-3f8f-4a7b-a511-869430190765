import fs from 'fs';
import path from 'path';
import vision from '@google-cloud/vision';
import Tesseract from 'tesseract.js';
import { OCR_CONFIG, getConfigForPair, cleanOCRText, extractPricesWithPatterns } from '../config/ocrConfig.js';

const __dirname = path.resolve();

// Configuración del cliente de Google Vision (opcional)
let client = null;
try {
  const keyPath = path.join(__dirname, 'backend', 'google-key.json');
  if (fs.existsSync(keyPath)) {
    client = new vision.ImageAnnotatorClient({ keyFilename: keyPath });
    console.log('✅ Google Vision configurado');
  }
} catch (error) {
  console.log('⚠️ Google Vision no disponible, usando Tesseract');
}

// Pares de divisas soportados con sus características
const PARES_CONFIG = {
  'XAUUSD': { tipo: 'metal', decimales: 2, pipValue: 0.01, rangoNormal: [1800, 2200] },
  'GBPJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [140, 200] },
  'EURUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.9, 1.3] },
  'USDJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [100, 160] },
  'BTCUSD': { tipo: 'crypto', decimales: 2, pipValue: 1, rangoNormal: [20000, 100000] },
  'ETHUSD': { tipo: 'crypto', decimales: 2, pipValue: 0.01, rangoNormal: [1000, 5000] },
  'AUDUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.6, 0.9] },
  'NZDUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [0.5, 0.8] },
  'USDCAD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [1.2, 1.5] },
  'EURJPY': { tipo: 'forex', decimales: 3, pipValue: 0.001, rangoNormal: [120, 170] },
  'GBPUSD': { tipo: 'forex', decimales: 5, pipValue: 0.00001, rangoNormal: [1.1, 1.5] },
  'SPX500': { tipo: 'indice', decimales: 2, pipValue: 0.01, rangoNormal: [3000, 6000] },
  'NAS100': { tipo: 'indice', decimales: 2, pipValue: 0.01, rangoNormal: [10000, 20000] },
  'US30': { tipo: 'indice', decimales: 2, pipValue: 0.01, rangoNormal: [25000, 40000] }
};

// Función para detectar par de divisas con mayor precisión
const detectarPar = (texto) => {
  const textoLimpio = texto.replace(/[^A-Z0-9]/g, '');

  // Buscar coincidencias exactas primero
  for (const par of Object.keys(PARES_CONFIG)) {
    if (textoLimpio.includes(par)) {
      return par;
    }
  }

  // Buscar con tolerancia a errores OCR
  const candidatos = texto.match(/[A-Z]{4,7}/g) || [];
  for (const candidato of candidatos) {
    for (const par of Object.keys(PARES_CONFIG)) {
      if (calcularSimilitud(candidato, par) >= 0.8) {
        return par;
      }
    }
  }

  return null;
};

// Función de similitud mejorada
const calcularSimilitud = (str1, str2) => {
  const len1 = str1.length;
  const len2 = str2.length;
  const maxLen = Math.max(len1, len2);

  if (maxLen === 0) return 1;

  const distance = levenshteinDistance(str1, str2);
  return (maxLen - distance) / maxLen;
};

const levenshteinDistance = (str1, str2) => {
  const matrix = Array(str2.length + 1).fill().map(() => Array(str1.length + 1).fill(0));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j - 1][i] + 1,
        matrix[j][i - 1] + 1,
        matrix[j - 1][i - 1] + cost
      );
    }
  }

  return matrix[str2.length][str1.length];
};

// Función para extraer precios con mayor precisión usando configuración avanzada
const extraerPrecios = (texto, par) => {
  if (!par) return [];

  const config = getConfigForPair(par);
  if (!config) return [];

  // Limpiar texto OCR
  const textoLimpio = cleanOCRText(texto);

  // Extraer precios usando patrones específicos
  const precios = extractPricesWithPatterns(textoLimpio, config.patterns);

  // Filtrar precios dentro del rango esperado
  return precios
    .filter(precio => precio >= config.minPrice && precio <= config.maxPrice)
    .filter((precio, index, arr) => arr.indexOf(precio) === index) // Eliminar duplicados
    .sort((a, b) => a - b);
};

export const analizarImagenOCR = async (req, res) => {
  try {
    const imagePath = req.file.path;
    let textoDetectado = '';

    // Intentar con Google Vision primero, luego Tesseract
    if (client) {
      try {
        const [result] = await client.textDetection(imagePath);
        const detections = result.textAnnotations;
        textoDetectado = detections.length > 0 ? detections[0].description : '';
        console.log('🧠 Google Vision - TEXTO DETECTADO:', textoDetectado);
      } catch (error) {
        console.log('⚠️ Google Vision falló, usando Tesseract');
      }
    }

    // Si Google Vision no funcionó, usar Tesseract con configuración optimizada
    if (!textoDetectado) {
      const result = await Tesseract.recognize(imagePath, OCR_CONFIG.tesseract.lang, {
        logger: m => console.log('📊 Tesseract:', m.status, m.progress),
        ...OCR_CONFIG.tesseract.options
      });
      textoDetectado = result.data.text;
      console.log('🧠 Tesseract - TEXTO DETECTADO:', textoDetectado);
    }

    if (!textoDetectado) {
      return res.json({
        mensaje: 'No se pudo extraer texto de la imagen.',
        par: null,
        precioEntrada: null,
        sl: null,
        tp1: null,
        tp2: null,
        tp3: null
      });
    }

    // Detectar par de divisas
    const parDetectado = detectarPar(textoDetectado);

    if (!parDetectado) {
      return res.json({
        mensaje: 'No se detectó un par de divisas válido en la imagen.',
        par: null,
        precioEntrada: null,
        sl: null,
        tp1: null,
        tp2: null,
        tp3: null,
        textoDetectado: textoDetectado.substring(0, 200) // Para debug
      });
    }

    // Extraer precios
    const precios = extraerPrecios(textoDetectado, parDetectado);

    if (precios.length < 2) {
      return res.json({
        mensaje: `Se detectó ${parDetectado} pero no suficientes precios válidos.`,
        par: parDetectado,
        precioEntrada: null,
        sl: null,
        tp1: null,
        tp2: null,
        tp3: null,
        preciosDetectados: precios,
        textoDetectado: textoDetectado.substring(0, 200)
      });
    }

    // Aplicar estrategia RunningPips
    const analisis = aplicarEstrategiaRunningPips(parDetectado, precios);

    res.json({
      mensaje: 'Análisis completado con éxito',
      par: parDetectado,
      ...analisis,
      preciosDetectados: precios,
      totalPrecios: precios.length
    });

  } catch (error) {
    console.error('❌ Error en OCR:', error);
    res.status(500).json({
      mensaje: 'Error al analizar la imagen.',
      error: error.message
    });
  } finally {
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
  }
};

// Implementación de la estrategia RunningPips
const aplicarEstrategiaRunningPips = (par, precios) => {
  const config = PARES_CONFIG[par];
  if (!config || precios.length < 2) {
    return {
      precioEntrada: null,
      sl: null,
      tp1: null,
      tp2: null,
      tp3: null,
      direccion: null,
      justificacion: 'Datos insuficientes para análisis'
    };
  }

  // Ordenar precios para identificar estructura
  const preciosOrdenados = [...precios].sort((a, b) => a - b);
  const precioMinimo = preciosOrdenados[0];
  const precioMaximo = preciosOrdenados[preciosOrdenados.length - 1];
  const precioMedio = preciosOrdenados[Math.floor(preciosOrdenados.length / 2)];

  // Calcular rango de precios
  const rango = precioMaximo - precioMinimo;
  const volatilidad = rango / precioMedio;

  // Determinar dirección basada en estructura de precios
  let direccion = 'BUY'; // Por defecto BUY
  let precioEntrada = precioMedio;
  let justificacion = '';

  // Análisis de estructura institucional
  if (volatilidad > 0.02) { // Alta volatilidad
    // Buscar rompimiento de estructura (BOS)
    const tercioSuperior = precioMinimo + (rango * 0.7);
    const tercioInferior = precioMinimo + (rango * 0.3);

    if (precioMedio > tercioSuperior) {
      direccion = 'BUY';
      precioEntrada = tercioInferior; // Entrada en retroceso
      justificacion = '📈 BOS alcista detectado - Entrada en zona de demanda';
    } else if (precioMedio < tercioInferior) {
      direccion = 'SELL';
      precioEntrada = tercioSuperior; // Entrada en retroceso
      justificacion = '📉 BOS bajista detectado - Entrada en zona de oferta';
    } else {
      direccion = 'BUY';
      precioEntrada = precioMinimo + (rango * 0.2);
      justificacion = '⚖️ Consolidación - Entrada en soporte';
    }
  } else {
    // Baja volatilidad - buscar acumulación
    direccion = 'BUY';
    precioEntrada = precioMinimo + (rango * 0.3);
    justificacion = '🔄 Acumulación institucional - Preparación para impulso';
  }

  // Calcular SL y TPs según la estrategia RunningPips
  let sl, tp1, tp2, tp3;

  if (direccion === 'BUY') {
    // SL debajo del mínimo reciente
    sl = precioMinimo - (config.pipValue * getPipsForPair(par, 'sl'));

    // TPs escalonados hacia liquidez superior
    const distanciaTP = (precioMaximo - precioEntrada) / 3;
    tp1 = precioEntrada + distanciaTP;
    tp2 = precioEntrada + (distanciaTP * 2);
    tp3 = precioMaximo + (config.pipValue * getPipsForPair(par, 'extension'));

  } else { // SELL
    // SL encima del máximo reciente
    sl = precioMaximo + (config.pipValue * getPipsForPair(par, 'sl'));

    // TPs escalonados hacia liquidez inferior
    const distanciaTP = (precioEntrada - precioMinimo) / 3;
    tp1 = precioEntrada - distanciaTP;
    tp2 = precioEntrada - (distanciaTP * 2);
    tp3 = precioMinimo - (config.pipValue * getPipsForPair(par, 'extension'));
  }

  // Formatear según decimales del par
  const formatear = (precio) => parseFloat(precio.toFixed(config.decimales));

  return {
    precioEntrada: formatear(precioEntrada),
    sl: formatear(sl),
    tp1: formatear(tp1),
    tp2: formatear(tp2),
    tp3: formatear(tp3),
    direccion,
    justificacion,
    volatilidad: parseFloat((volatilidad * 100).toFixed(2)),
    rango: formatear(rango)
  };
};

// Función para obtener pips según el par y tipo de nivel
const getPipsForPair = (par, tipo) => {
  const pipsConfig = {
    'XAUUSD': { sl: 200, extension: 300 },
    'GBPJPY': { sl: 30, extension: 50 },
    'EURUSD': { sl: 20, extension: 30 },
    'USDJPY': { sl: 25, extension: 40 },
    'BTCUSD': { sl: 500, extension: 1000 },
    'ETHUSD': { sl: 50, extension: 100 },
    'AUDUSD': { sl: 20, extension: 30 },
    'NZDUSD': { sl: 20, extension: 30 },
    'USDCAD': { sl: 20, extension: 30 },
    'EURJPY': { sl: 30, extension: 50 },
    'GBPUSD': { sl: 25, extension: 40 },
    'SPX500': { sl: 50, extension: 100 },
    'NAS100': { sl: 100, extension: 200 },
    'US30': { sl: 100, extension: 200 }
  };

  return pipsConfig[par]?.[tipo] || 20;
};
