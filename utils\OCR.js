import Tesseract from 'tesseract.js';

const paresPermitidos = [
  'XAUUSD', 'GBPJPY', 'EURUSD', 'USDJPY', 'BTCUSD',
  'ETHUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'EURJPY',
  'GBPUSD', 'SPX500', 'NAS100', 'US30'
];

// 🧠 Función para calcular similitud (Levenshtein)
const distancia = (a, b) => {
  if (!a || !b) return 99;
  const matrix = Array.from({ length: b.length + 1 }, (_, i) => [i]);
  for (let j = 0; j <= a.length; j++) matrix[0][j] = j;
  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      const cost = a[j - 1] === b[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  return matrix[b.length][a.length];
};

export const extraerPreciosDesdeImagen = async (uri) => {
  try {
    const resultadoOCR = await Tesseract.recognize(uri, 'eng', {
      logger: m => console.log('📊 OCR progreso:', m),
    });

    let texto = resultadoOCR.data.text;
    console.log("🔍 TEXTO OCR:", texto);

    // 🔎 Buscar posibles pares en texto con similitud
    const candidatos = texto.toUpperCase().match(/[A-Z]{4,7}/g) || [];
    let mejorPar = 'No detectado';
    let menorDistancia = 3;

    for (const palabra of candidatos) {
      for (const par of paresPermitidos) {
        const dist = distancia(palabra, par);
        if (dist < menorDistancia) {
          menorDistancia = dist;
          mejorPar = par;
        }
      }
    }

    // 🔢 Buscar precios reales (3 o más)
    const preciosDetectados = texto
      .replace(/[^0-9.,]/g, ' ')
      .match(/\d{3,5}[.,]\d{1,5}/g)
      ?.map(p => parseFloat(p.replace(',', '.')))
      .filter(n => n > 10);

    if (!preciosDetectados || preciosDetectados.length < 3) {
      return null;
    }

    preciosDetectados.sort((a, b) => a - b);
    const precioEntrada = preciosDetectados[Math.floor(preciosDetectados.length / 2)];
    const sl = Math.min(...preciosDetectados);
    const tp1 = precioEntrada + 10;
    const tp2 = precioEntrada + 20;
    const tp3 = precioEntrada + 30;

    return {
      par: mejorPar,
      precioEntrada,
      sl,
      tp1,
      tp2,
      tp3
    };

  } catch (error) {
    console.error("❌ Error en OCR:", error.message || error);
    return null;
  }
};
