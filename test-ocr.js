// Script de prueba para verificar la funcionalidad OCR
import { detectarPar, extraerPrecios } from './backend/controllers/ocrController.js';

// Texto de prueba simulando OCR de un gráfico
const textosPrueba = [
  {
    nombre: 'XAUUSD Chart',
    texto: 'XAUUSD 1987.65 1985.30 1990.45 1992.10 H4 Chart',
    esperado: { par: 'XAUUSD', precios: [1985.30, 1987.65, 1990.45, 1992.10] }
  },
  {
    nombre: 'EURUSD Chart',
    texto: 'EURUSD 1.12345 1.12180 1.12567 1.12890 M15',
    esperado: { par: 'EURUSD', precios: [1.12180, 1.12345, 1.12567, 1.12890] }
  },
  {
    nombre: 'GBPJPY Chart',
    texto: 'GBPJPY 156.789 155.234 158.456 159.123 H1',
    esperado: { par: 'GBPJPY', precios: [155.234, 156.789, 158.456, 159.123] }
  },
  {
    nombre: 'BTCUSD Chart',
    texto: 'BTCUSD 45678.90 44567.80 46789.10 47890.20',
    esperado: { par: 'BTCUSD', precios: [44567.80, 45678.90, 46789.10, 47890.20] }
  }
];

console.log('🧪 Iniciando pruebas de OCR...\n');

for (const prueba of textosPrueba) {
  console.log(`📊 Probando: ${prueba.nombre}`);
  console.log(`📝 Texto: ${prueba.texto}`);
  
  // Probar detección de par
  const parDetectado = detectarPar(prueba.texto);
  console.log(`🎯 Par detectado: ${parDetectado}`);
  console.log(`✅ Par esperado: ${prueba.esperado.par}`);
  
  if (parDetectado === prueba.esperado.par) {
    console.log('✅ Par detectado correctamente');
  } else {
    console.log('❌ Error en detección de par');
  }
  
  // Probar extracción de precios
  const preciosDetectados = extraerPrecios(prueba.texto, parDetectado);
  console.log(`💰 Precios detectados: ${preciosDetectados.join(', ')}`);
  console.log(`✅ Precios esperados: ${prueba.esperado.precios.join(', ')}`);
  
  if (preciosDetectados.length >= 2) {
    console.log('✅ Precios extraídos correctamente');
  } else {
    console.log('❌ Error en extracción de precios');
  }
  
  console.log('─'.repeat(50));
}

console.log('\n🏁 Pruebas completadas');

// Función de prueba para la estrategia RunningPips
const probarEstrategia = () => {
  console.log('\n🧠 Probando estrategia RunningPips...');
  
  const preciosPrueba = [1987.65, 1985.30, 1990.45, 1992.10];
  const par = 'XAUUSD';
  
  // Simular análisis
  const preciosOrdenados = [...preciosPrueba].sort((a, b) => a - b);
  const precioMinimo = preciosOrdenados[0];
  const precioMaximo = preciosOrdenados[preciosOrdenados.length - 1];
  const precioMedio = preciosOrdenados[Math.floor(preciosOrdenados.length / 2)];
  
  console.log(`📊 Par: ${par}`);
  console.log(`📈 Precios: ${preciosPrueba.join(', ')}`);
  console.log(`📉 Mínimo: ${precioMinimo}`);
  console.log(`📈 Máximo: ${precioMaximo}`);
  console.log(`⚖️ Medio: ${precioMedio}`);
  
  const rango = precioMaximo - precioMinimo;
  const volatilidad = (rango / precioMedio * 100).toFixed(2);
  
  console.log(`📊 Rango: ${rango.toFixed(2)}`);
  console.log(`📊 Volatilidad: ${volatilidad}%`);
  
  // Determinar dirección
  const tercioSuperior = precioMinimo + (rango * 0.7);
  const tercioInferior = precioMinimo + (rango * 0.3);
  
  let direccion = 'BUY';
  let precioEntrada = precioMedio;
  
  if (precioMedio > tercioSuperior) {
    direccion = 'BUY';
    precioEntrada = tercioInferior;
  } else if (precioMedio < tercioInferior) {
    direccion = 'SELL';
    precioEntrada = tercioSuperior;
  }
  
  console.log(`🎯 Dirección: ${direccion}`);
  console.log(`💰 Entrada: ${precioEntrada.toFixed(2)}`);
  
  // Calcular SL y TPs
  let sl, tp1, tp2, tp3;
  
  if (direccion === 'BUY') {
    sl = precioMinimo - 2.00; // SL debajo del mínimo
    const distanciaTP = (precioMaximo - precioEntrada) / 3;
    tp1 = precioEntrada + distanciaTP;
    tp2 = precioEntrada + (distanciaTP * 2);
    tp3 = precioMaximo + 3.00; // Extensión
  } else {
    sl = precioMaximo + 2.00; // SL encima del máximo
    const distanciaTP = (precioEntrada - precioMinimo) / 3;
    tp1 = precioEntrada - distanciaTP;
    tp2 = precioEntrada - (distanciaTP * 2);
    tp3 = precioMinimo - 3.00; // Extensión
  }
  
  console.log(`🛑 SL: ${sl.toFixed(2)}`);
  console.log(`🎯 TP1: ${tp1.toFixed(2)}`);
  console.log(`🎯 TP2: ${tp2.toFixed(2)}`);
  console.log(`🎯 TP3: ${tp3.toFixed(2)}`);
  
  console.log('\n✅ Estrategia RunningPips aplicada correctamente');
};

probarEstrategia();
